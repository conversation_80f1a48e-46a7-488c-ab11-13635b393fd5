package main

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/cookiejar"
	"strings"
	"time"
)

type Fetcher struct {
	client    *http.Client
	userAgent string
	sessionID string
}

func NewFetcher() *Fetcher {
	// Create cookie jar for session management
	jar, _ := cookiejar.New(nil)
	
	return &Fetcher{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Jar:     jar,
		},
		userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		sessionID: fmt.Sprintf("sess_%d_%d", time.Now().Unix(), time.Now().Nanosecond()),
	}
}

// SetUserAgent sets a custom user agent
func (f *Fetcher) SetUserAgent(ua string) {
	f.userAgent = ua
}

func (f *Fetcher) FetchDecisionIDs(pageNumber, pageSize int) ([]APISearchItem, error) {
	searchData := APISearchData{
		ArananKelime:       "",
		YargitayMah:        "Büyük Genel Kurulu",
		Hukuk:              "23. Hukuk Dairesi",
		Ceza:               "23. Ceza Dairesi",
		EsasYil:            "",
		EsasIlkSiraNo:      "",
		EsasSonSiraNo:      "",
		KararYil:           "",
		KararIlkSiraNo:     "",
		KararSonSiraNo:     "",
		BaslangicTarihi:    "",
		BitisTarihi:        "",
		Siralama:           "1",
		SiralamaDirection:  "desc",
		BirimYrgKurulDaire: "Hukuk Genel Kurulu+Ceza Genel Kurulu+Ceza Daireleri Başkanlar Kurulu+Hukuk Daireleri Başkanlar Kurulu+Büyük Genel Kurulu",
		BirimYrgHukukDaire: "1. Hukuk Dairesi+2. Hukuk Dairesi+3. Hukuk Dairesi+4. Hukuk Dairesi+5. Hukuk Dairesi+6. Hukuk Dairesi+7. Hukuk Dairesi+8. Hukuk Dairesi+9. Hukuk Dairesi+10. Hukuk Dairesi+11. Hukuk Dairesi+12. Hukuk Dairesi+13. Hukuk Dairesi+14. Hukuk Dairesi+15. Hukuk Dairesi+16. Hukuk Dairesi+17. Hukuk Dairesi+18. Hukuk Dairesi+19. Hukuk Dairesi+20. Hukuk Dairesi+21. Hukuk Dairesi+22. Hukuk Dairesi+23. Hukuk Dairesi",
		BirimYrgCezaDaire:  "1. Ceza Dairesi+2. Ceza Dairesi+3. Ceza Dairesi+4. Ceza Dairesi+5. Ceza Dairesi+6. Ceza Dairesi+7. Ceza Dairesi+8. Ceza Dairesi+9. Ceza Dairesi+10. Ceza Dairesi+11. Ceza Dairesi+12. Ceza Dairesi+13. Ceza Dairesi+14. Ceza Dairesi+15. Ceza Dairesi+16. Ceza Dairesi+17. Ceza Dairesi+18. Ceza Dairesi+19. Ceza Dairesi+20. Ceza Dairesi+21. Ceza Dairesi+22. Ceza Dairesi+23. Ceza Dairesi",
		PageSize:           pageSize,
		PageNumber:         pageNumber,
	}

	requestBody := APISearchRequest{
		Data: searchData,
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("JSON hazırlanamadı: %w", err)
	}

	req, err := http.NewRequest("POST", "https://karararama.yargitay.gov.tr/aramadetaylist", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("istek oluşturulamadı: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("User-Agent", f.userAgent)
	req.Header.Set("Origin", "https://karararama.yargitay.gov.tr")
	req.Header.Set("Referer", "https://karararama.yargitay.gov.tr/")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("X-Session-ID", f.sessionID)

	resp, err := f.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("API çağrısı başarısız: %w", err)
	}
	defer resp.Body.Close()

	// Handle compressed responses
	var reader io.Reader = resp.Body
	if strings.Contains(resp.Header.Get("Content-Encoding"), "gzip") {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("gzip reader oluşturulamadı: %w", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}
	
	body, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("yanıt okunamadı: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		log.Printf("API Hatası - Status: %d, Body: %s", resp.StatusCode, string(body))
		return nil, fmt.Errorf("API hatası: %d", resp.StatusCode)
	}

	var apiResponse APISearchResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("JSON parse hatası: %w", err)
	}

	// Return full items with metadata instead of just IDs
	items := apiResponse.Data.Data
	
	log.Printf("📋 Sayfa %d: %d karar bulundu (Toplam: %d)", 
		pageNumber, len(items), apiResponse.Data.RecordsTotal)
	
	return items, nil
}

func (f *Fetcher) FetchDecisionHTML(decisionID string) (string, error) {
	url := fmt.Sprintf("https://karararama.yargitay.gov.tr/getDokuman?id=%s", decisionID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("istek oluşturulamadı: %w", err)
	}

	req.Header.Set("User-Agent", f.userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Referer", "https://karararama.yargitay.gov.tr/")
	req.Header.Set("X-Session-ID", f.sessionID)

	resp, err := f.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("API çağrısı başarısız: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API hatası: %d", resp.StatusCode)
	}

	// Handle compressed responses
	var reader io.Reader = resp.Body
	if strings.Contains(resp.Header.Get("Content-Encoding"), "gzip") {
		gzReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return "", fmt.Errorf("gzip reader oluşturulamadı: %w", err)
		}
		defer gzReader.Close()
		reader = gzReader
	}
	
	body, err := io.ReadAll(reader)
	if err != nil {
		return "", fmt.Errorf("yanıt okunamadı: %w", err)
	}

	// Convert to string for processing
	bodyStr := string(body)

	// Check for CAPTCHA error
	if strings.Contains(bodyStr, "DisplayCaptcha") || strings.Contains(bodyStr, "CAPTCHA") {
		return "", fmt.Errorf("CAPTCHA required - rate limited by server")
	}
	
	// Check for runtime exceptions
	if strings.Contains(bodyStr, "ADALET_RUNTIME_EXCEPTION") {
		return "", fmt.Errorf("server runtime error - possibly rate limited")
	}

	// Check if response is XML wrapped HTML
	if strings.HasPrefix(bodyStr, "<AdaletResponseDto>") {
		// Extract data from XML wrapper
		start := strings.Index(bodyStr, "<data>")
		end := strings.Index(bodyStr, "</data>")
		if start != -1 && end != -1 && start < end {
			htmlContent := bodyStr[start+6 : end]
			
			// Check if the extracted content is an error
			if strings.Contains(htmlContent, "ERROR") || strings.Contains(htmlContent, "EXCEPTION") {
				previewLen := 100
				if len(htmlContent) < previewLen {
					previewLen = len(htmlContent)
				}
				return "", fmt.Errorf("server returned error: %s", htmlContent[:previewLen])
			}
			
			// Unescape HTML entities
			htmlContent = strings.ReplaceAll(htmlContent, "&lt;", "<")
			htmlContent = strings.ReplaceAll(htmlContent, "&gt;", ">")
			htmlContent = strings.ReplaceAll(htmlContent, "&amp;", "&")
			htmlContent = strings.ReplaceAll(htmlContent, "&quot;", "\"")
			return htmlContent, nil
		}
	}
	
	// Try JSON parsing for backward compatibility
	var docResponse APIDocumentResponse
	if err := json.Unmarshal(body, &docResponse); err != nil {
		// If it starts with '<', it's likely HTML
		if len(body) > 0 && body[0] == '<' {
			// Return as is if it's raw HTML
			return string(body), nil
		}
		return "", fmt.Errorf("parse hatası: %w", err)
	}

	if docResponse.Data == "" {
		return "", fmt.Errorf("boş içerik")
	}

	return docResponse.Data, nil
}