# Production Dockerfile with multi-stage build
# Stage 1: Builder
FROM golang:1.24-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git gcc musl-dev

# Set working directory
WORKDIR /build

# Copy go mod files and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the binary with optimizations
RUN CGO_ENABLED=1 GOOS=linux go build \
    -ldflags="-w -s" \
    -o hukukat .

# Stage 2: Runtime
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S hukukat && \
    adduser -u 1001 -S hukukat -G hukukat

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /build/hukukat .
COPY --from=builder /build/static ./static

# Create data directory and set permissions
RUN mkdir -p /app/data && \
    chown -R hukukat:hukukat /app

# Switch to non-root user
USER hukukat

# Expose port
EXPOSE 8020

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8020/health || exit 1

# Volume for persistent data
VOLUME ["/app/data"]

# Environment variables for scraping
ENV AUTO_SCRAPE=true
ENV SCRAPE_START=1
ENV SCRAPE_END=933388
ENV SCRAPE_SIZE=10

# Default command - run with auto scrape
CMD ["./hukukat"]