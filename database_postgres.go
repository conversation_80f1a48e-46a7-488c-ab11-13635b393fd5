package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	_ "github.com/lib/pq"
)

// PostgresDB handles PostgreSQL with pgvector operations
type PostgresDB struct {
	db *sql.DB
}

// NewPostgresDB creates a new PostgreSQL connection with pgvector support
func NewPostgresDB() (*PostgresDB, error) {
	// Get connection parameters from environment
	host := os.Getenv("POSTGRES_HOST")
	if host == "" {
		host = "localhost"
	}
	port := os.Getenv("POSTGRES_PORT")
	if port == "" {
		port = "5432"
	}
	user := os.Getenv("POSTGRES_USER")
	if user == "" {
		user = "hukukat"
	}
	password := os.Getenv("POSTGRES_PASSWORD")
	if password == "" {
		password = "hukukat_secret_2024"
	}
	dbname := os.Getenv("POSTGRES_DB")
	if dbname == "" {
		dbname = "hukukat_db"
	}

	// Build connection string
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	// Connect to database
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to postgres: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping postgres: %w", err)
	}

	pdb := &PostgresDB{db: db}

	// Initialize database schema
	if err := pdb.initSchema(); err != nil {
		return nil, fmt.Errorf("failed to initialize schema: %w", err)
	}

	log.Println("✅ PostgreSQL with pgvector ready")
	return pdb, nil
}

// initSchema creates tables and enables pgvector
func (p *PostgresDB) initSchema() error {
	// Enable pgvector extension
	if _, err := p.db.Exec("CREATE EXTENSION IF NOT EXISTS vector"); err != nil {
		return fmt.Errorf("failed to create vector extension: %w", err)
	}

	// Create decisions table (mirrors SQLite structure)
	_, err := p.db.Exec(`
		CREATE TABLE IF NOT EXISTS decisions (
			id SERIAL PRIMARY KEY,
			decision_id TEXT UNIQUE NOT NULL,
			daire TEXT,
			esas_no TEXT,
			karar_no TEXT,
			karar_tarihi TEXT,
			mahkeme_adi TEXT,
			ilam_tarihi TEXT,
			decision_text TEXT,
			raw_html TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create decisions table: %w", err)
	}

	// Create embeddings table for vector search
	_, err = p.db.Exec(`
		CREATE TABLE IF NOT EXISTS decision_embeddings (
			id SERIAL PRIMARY KEY,
			decision_id TEXT UNIQUE NOT NULL REFERENCES decisions(decision_id) ON DELETE CASCADE,
			embedding vector(1536),  -- OpenAI ada-002 dimension
			embedding_model TEXT DEFAULT 'ada-002',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create embeddings table: %w", err)
	}

	// Create indexes
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_decisions_decision_id ON decisions(decision_id)",
		"CREATE INDEX IF NOT EXISTS idx_decisions_daire ON decisions(daire)",
		"CREATE INDEX IF NOT EXISTS idx_decisions_karar_tarihi ON decisions(karar_tarihi)",
		"CREATE INDEX IF NOT EXISTS idx_embeddings_decision_id ON decision_embeddings(decision_id)",
		// Vector similarity search index (IVFFlat)
		"CREATE INDEX IF NOT EXISTS idx_embeddings_vector ON decision_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100)",
	}

	for _, idx := range indexes {
		if _, err := p.db.Exec(idx); err != nil {
			log.Printf("Warning: Failed to create index: %v", err)
		}
	}

	return nil
}

// SaveDecision saves a decision to PostgreSQL
func (p *PostgresDB) SaveDecision(decision *Decision) error {
	query := `
		INSERT INTO decisions (decision_id, daire, esas_no, karar_no, karar_tarihi, 
			mahkeme_adi, ilam_tarihi, decision_text, raw_html)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		ON CONFLICT (decision_id) DO UPDATE SET
			daire = EXCLUDED.daire,
			esas_no = EXCLUDED.esas_no,
			karar_no = EXCLUDED.karar_no,
			karar_tarihi = EXCLUDED.karar_tarihi,
			mahkeme_adi = EXCLUDED.mahkeme_adi,
			ilam_tarihi = EXCLUDED.ilam_tarihi,
			decision_text = EXCLUDED.decision_text,
			raw_html = EXCLUDED.raw_html
	`

	_, err := p.db.Exec(query,
		decision.DecisionID,
		decision.Daire,
		decision.EsasNo,
		decision.KararNo,
		decision.KararTarihi,
		decision.MahkemeAdi,
		decision.IlamTarihi,
		decision.DecisionText,
		decision.RawHTML,
	)

	return err
}

// SaveEmbedding saves an embedding vector for a decision
func (p *PostgresDB) SaveEmbedding(decisionID string, embedding []float32, model string) error {
	query := `
		INSERT INTO decision_embeddings (decision_id, embedding, embedding_model)
		VALUES ($1, $2, $3)
		ON CONFLICT (decision_id) DO UPDATE SET
			embedding = EXCLUDED.embedding,
			embedding_model = EXCLUDED.embedding_model,
			created_at = CURRENT_TIMESTAMP
	`

	_, err := p.db.Exec(query, decisionID, embedding, model)
	return err
}

// SearchSimilar finds similar decisions using vector similarity
func (p *PostgresDB) SearchSimilar(queryEmbedding []float32, limit int) ([]SimilarDecision, error) {
	query := `
		SELECT 
			d.decision_id,
			d.daire,
			d.esas_no,
			d.karar_no,
			d.karar_tarihi,
			d.decision_text,
			1 - (e.embedding <=> $1::vector) as similarity
		FROM decision_embeddings e
		JOIN decisions d ON d.decision_id = e.decision_id
		ORDER BY e.embedding <=> $1::vector
		LIMIT $2
	`

	rows, err := p.db.Query(query, queryEmbedding, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []SimilarDecision
	for rows.Next() {
		var sd SimilarDecision
		err := rows.Scan(
			&sd.DecisionID,
			&sd.Daire,
			&sd.EsasNo,
			&sd.KararNo,
			&sd.KararTarihi,
			&sd.DecisionText,
			&sd.Similarity,
		)
		if err != nil {
			return nil, err
		}
		results = append(results, sd)
	}

	return results, nil
}

// GetDecisionsWithoutEmbeddings returns decisions that don't have embeddings yet
func (p *PostgresDB) GetDecisionsWithoutEmbeddings(limit int) ([]Decision, error) {
	query := `
		SELECT d.decision_id, d.daire, d.esas_no, d.karar_no, 
			d.karar_tarihi, d.mahkeme_adi, d.ilam_tarihi, d.decision_text
		FROM decisions d
		LEFT JOIN decision_embeddings e ON d.decision_id = e.decision_id
		WHERE e.id IS NULL
		LIMIT $1
	`

	rows, err := p.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var decisions []Decision
	for rows.Next() {
		var d Decision
		err := rows.Scan(
			&d.DecisionID,
			&d.Daire,
			&d.EsasNo,
			&d.KararNo,
			&d.KararTarihi,
			&d.MahkemeAdi,
			&d.IlamTarihi,
			&d.DecisionText,
		)
		if err != nil {
			return nil, err
		}
		decisions = append(decisions, d)
	}

	return decisions, nil
}

// MigrateFromSQLite copies data from SQLite to PostgreSQL
func (p *PostgresDB) MigrateFromSQLite(sqliteDB *Database) error {
	// Get all decisions from SQLite
	rows, err := sqliteDB.db.Query(`
		SELECT decision_id, daire, esas_no, karar_no, karar_tarihi, 
			mahkeme_adi, ilam_tarihi, decision_text, raw_html
		FROM decisions
	`)
	if err != nil {
		return fmt.Errorf("failed to read from sqlite: %w", err)
	}
	defer rows.Close()

	count := 0
	for rows.Next() {
		var d Decision
		err := rows.Scan(
			&d.DecisionID,
			&d.Daire,
			&d.EsasNo,
			&d.KararNo,
			&d.KararTarihi,
			&d.MahkemeAdi,
			&d.IlamTarihi,
			&d.DecisionText,
			&d.RawHTML,
		)
		if err != nil {
			log.Printf("Failed to scan row: %v", err)
			continue
		}

		if err := p.SaveDecision(&d); err != nil {
			log.Printf("Failed to save decision %s: %v", d.DecisionID, err)
			continue
		}
		count++
	}

	log.Printf("✅ Migrated %d decisions from SQLite to PostgreSQL", count)
	return nil
}

// Close closes the database connection
func (p *PostgresDB) Close() error {
	return p.db.Close()
}

