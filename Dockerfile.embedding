# BGE-M3 Embedding Service Dockerfile
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    wget \
    curl \
    zlib1g-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy embedding service
COPY embedding_service.py .

# Create non-root user for security
RUN useradd -m -u 1001 embedder
RUN chown -R embedder:embedder /app
USER embedder

# Expose port
EXPOSE 8021

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8021/health || exit 1

# Start the service with Gun<PERSON>
CMD ["gunicorn", "--bind", "0.0.0.0:8021", "--workers", "1", "--threads", "4", "--timeout", "120", "embedding_service:app"]