# Docker Setup for Hukukat

## Quick Start

### Development (with hot reload)
```bash
# Start development server with Air hot reload
docker-compose --profile dev up

# Or build and run separately
docker-compose --profile dev build
docker-compose --profile dev up
```

### Development with Auto-Scraper
```bash
# Start with automatic scraping enabled
AUTO_SCRAPE=true docker-compose up

# Or with custom parameters
AUTO_SCRAPE=true SCRAPE_START=1 SCRAPE_END=1000 SCRAPE_SIZE=50 docker-compose up

# Using .env file
echo "AUTO_SCRAPE=true" > .env
echo "SCRAPE_START=1" >> .env
echo "SCRAPE_END=10000" >> .env
echo "SCRAPE_SIZE=20" >> .env
docker-compose up
```

### Production
```bash
# Start production server
docker-compose --profile prod up -d

# Start production with auto-scraper
AUTO_SCRAPE=true docker-compose --profile prod up -d

# View logs
docker-compose --profile prod logs -f
```

### Scraping
```bash
# Run adaptive scraper (safe, default)
docker-compose --profile scraper-adaptive up -d

# Run fast scraper (10 workers)
docker-compose --profile scraper-fast up -d

# Check scraper logs
docker-compose --profile scraper-adaptive logs -f
```

## Available Services

| Service | Profile | Description | Command |
|---------|---------|-------------|---------|
| `app-dev` | `dev` | Development with hot reload | `docker-compose --profile dev up` |
| `app` | `prod` | Production web server | `docker-compose --profile prod up` |
| `scraper` | `scraper` | Basic scraper | `docker-compose --profile scraper up` |
| `scraper-adaptive` | `scraper-adaptive` | Safe adaptive scraper | `docker-compose --profile scraper-adaptive up` |
| `scraper-fast` | `scraper-fast` | Fast multi-worker scraper | `docker-compose --profile scraper-fast up` |

## Commands

### Build
```bash
# Build development image
docker build -f Dockerfile.dev -t hukukat:dev .

# Build production image
docker build -f Dockerfile -t hukukat:latest .
```

### Run Standalone
```bash
# Development
docker run -p 8020:8020 -v $(pwd):/app hukukat:dev

# Production
docker run -p 8020:8020 -v hukukat-data:/app/data hukukat:latest

# Scraping
docker run -v hukukat-data:/app/data hukukat:latest \
  ./hukukat --no-server --start=1 --end=1000
```

### Data Management
```bash
# List volumes
docker volume ls | grep hukukat

# Inspect data volume
docker volume inspect hukukat-data-prod

# Backup database
docker run --rm -v hukukat-data-prod:/data \
  -v $(pwd):/backup alpine \
  tar czf /backup/hukukat-backup.tar.gz /data

# Restore database
docker run --rm -v hukukat-data-prod:/data \
  -v $(pwd):/backup alpine \
  tar xzf /backup/hukukat-backup.tar.gz -C /
```

### Monitoring
```bash
# View all containers
docker-compose ps

# View logs
docker-compose --profile prod logs -f

# Stats
docker stats hukukat-prod

# Health check
curl http://localhost:8020/health
```

## Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | `:8020` | HTTP server port |
| `ENV` | `production` | Environment (development/production) |
| `AUTO_SCRAPE` | `false` | Enable automatic scraping on startup |
| `SCRAPE_START` | `1` | Starting page for auto-scraper |
| `SCRAPE_END` | `100000` | Ending page for auto-scraper |
| `SCRAPE_SIZE` | `20` | Items per page for auto-scraper |
| `POSTGRES_HOST` | `postgres` | PostgreSQL host |
| `POSTGRES_PORT` | `5432` | PostgreSQL port |
| `POSTGRES_USER` | `hukukat` | PostgreSQL user |
| `POSTGRES_PASSWORD` | `hukukat_secret_2024` | PostgreSQL password |
| `POSTGRES_DB` | `hukukat_db` | PostgreSQL database |
| `EMBEDDING_SERVICE_URL` | `http://embedding-service:8021` | BGE-M3 embedding service URL |

## Volumes

- `data-dev`: Development database (SQLite)
- `data-prod`: Production database (SQLite)

## Networking

All services use `hukukat-network` for internal communication.

## Tips

1. **Development**: Code changes automatically trigger rebuild thanks to Air
2. **Production**: Uses multi-stage build for ~20MB final image
3. **Security**: Production runs as non-root user
4. **Health Check**: Automatic health monitoring in production
5. **Data Persistence**: All data stored in Docker volumes

## Troubleshooting

### Port already in use
```bash
# Check what's using port 8020
lsof -i :8020

# Use different port
PORT=:3000 docker-compose --profile prod up
```

### Permission issues
```bash
# Fix volume permissions
docker-compose down
docker volume rm hukukat-data-prod
docker-compose --profile prod up
```

### Build cache issues
```bash
# Rebuild without cache
docker-compose build --no-cache
```