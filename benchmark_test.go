package main

import (
	"context"
	"fmt"
	"log"
	"testing"
	"time"
)

// TestScraperPerformance compares normal vs optimized scraper
func TestScraperPerformance(t *testing.T) {
	// Setup database
	db, err := NewDatabase("data/test_decisions.db")
	if err != nil {
		t.Fatalf("Database setup failed: %v", err)
	}
	defer db.Close()

	pageNumber := 1
	pageSize := 10

	// Test 1: Normal scraper
	t.Run("Normal Scraper", func(t *testing.T) {
		scraper := &Scraper{
			db:       db,
			fetcher:  NewFetcher(),
			parser:   NewParser(),
			stopChan: make(chan bool),
		}

		start := time.Now()
		err := scraper.ProcessPage(pageNumber, pageSize)
		duration := time.Since(start)

		if err != nil {
			t.Errorf("Normal scraper failed: %v", err)
		}

		t.Logf("Normal Scraper: %v for %d items", duration, pageSize)
		t.Logf("Average per item: %v", duration/time.Duration(pageSize))
	})

	// Test 2: Optimized scraper with different worker counts
	workerCounts := []int{1, 3, 5, 10}
	
	for _, workers := range workerCounts {
		t.Run(fmt.Sprintf("Optimized Scraper (%d workers)", workers), func(t *testing.T) {
			optimized := NewOptimizedScraper(db, workers)
			
			start := time.Now()
			ctx := context.Background()
			err := optimized.ProcessPageConcurrent(ctx, pageNumber+workers, pageSize)
			duration := time.Since(start)

			if err != nil {
				t.Errorf("Optimized scraper failed: %v", err)
			}

			stats := optimized.GetStats()
			t.Logf("Optimized Scraper (%d workers): %v for %d items", workers, duration, pageSize)
			t.Logf("Stats: %+v", stats)
			t.Logf("Rate: %v items/second", stats["rate_per_second"])
		})
	}
}

// BenchmarkNormalScraper benchmarks the normal scraper
func BenchmarkNormalScraper(b *testing.B) {
	db, _ := NewDatabase("data/bench_decisions.db")
	defer db.Close()

	scraper := &Scraper{
		db:       db,
		fetcher:  NewFetcher(),
		parser:   NewParser(),
		stopChan: make(chan bool),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		scraper.ProcessPage(1, 5)
	}
}

// BenchmarkOptimizedScraper benchmarks the optimized scraper
func BenchmarkOptimizedScraper(b *testing.B) {
	db, _ := NewDatabase("data/bench_decisions.db")
	defer db.Close()

	optimized := NewOptimizedScraper(db, 5)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		optimized.ProcessPageConcurrent(ctx, 1, 5)
	}
}

// TestRateLimitAdjustment tests dynamic rate limiting
func TestRateLimitAdjustment(t *testing.T) {
	db, _ := NewDatabase("data/test_decisions.db")
	defer db.Close()

	optimized := NewOptimizedScraper(db, 3)
	
	// Simulate different error rates
	testCases := []struct {
		name      string
		errorRate float64
		expected  time.Duration
	}{
		{"Low error rate", 0.05, 100 * time.Millisecond},
		{"Medium error rate", 0.2, 200 * time.Millisecond},
		{"High error rate", 0.4, 500 * time.Millisecond},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Reset stats
			optimized.stats = &ScraperStats{StartTime: time.Now()}
			
			// Simulate processing with errors
			total := 100
			errors := int(float64(total) * tc.errorRate)
			
			for i := 0; i < total; i++ {
				if i < errors {
					optimized.stats.TotalErrors++
				} else {
					optimized.stats.TotalSuccess++
				}
				optimized.stats.TotalProcessed++
			}
			
			// Apply rate limit logic
			optimized.applyRateLimit()
			
			t.Logf("%s: Rate limit adjusted to %v", tc.name, optimized.rateLimit)
		})
	}
}

func TestMain(m *testing.M) {
	// Setup
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	
	// Run tests
	m.Run()
	
	// Cleanup
	// You can add cleanup code here if needed
}