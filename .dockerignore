# Git
.git
.gitignore
.github

# Documentation
*.md
LICENSE
docs/

# Data and binaries
data/
hukukat
*.exe
*.dll
*.so
*.dylib

# Test files
*_test.go
*.test
coverage.out
coverage.html

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Temporary files
tmp/
temp/
*.tmp
*.log

# Docker files (avoid recursive copies)
Dockerfile*
docker-compose*.yml
.dockerignore

# Air
.air.toml
tmp/

# Environment files
.env
.env.*