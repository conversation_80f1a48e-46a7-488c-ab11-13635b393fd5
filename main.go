package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

type Scraper struct {
	db               *Database
	fetcher          *Fetcher
	parser           *Parser
	stopChan         chan bool
	embeddingManager *EmbeddingManager
}

func NewScraper() (*Scraper, error) {
	db, err := NewDatabase()
	if err != nil {
		return nil, fmt.Errorf("veritabanı başlatılamadı: %w", err)
	}

	// Create a single embedding manager instance with worker pool
	embeddingManager := NewEmbeddingManager(db)

	return &Scraper{
		db:               db,
		fetcher:          NewFetcher(),
		parser:           NewParser(),
		stopChan:         make(chan bool),
		embeddingManager: embeddingManager,
	}, nil
}

func (s *Scraper) ProcessPage(pageNumber, pageSize int) error {
	log.Printf("📄 Sayfa %d işleniyor (boyut: %d)...", pageNumber, pageSize)

	items, err := s.fetcher.FetchDecisionIDs(pageNumber, pageSize)
	if err != nil {
		return fmt.Errorf("sayfa %d alınamadı: %w", pageNumber, err)
	}

	if len(items) == 0 {
		log.Printf("⚠️ Sayfa %d'de karar bulunamadı", pageNumber)
		return nil
	}

	successCount := 0
	skipCount := 0
	errorCount := 0

	for i, item := range items {
		select {
		case <-s.stopChan:
			log.Println("⏹️ İşlem durduruldu")
			return nil
		default:
		}

		exists, err := s.db.IsDecisionExists(item.ID)
		if err == nil && exists {
			log.Printf("⏭️ [%d/%d] ID %s zaten mevcut", i+1, len(items), item.ID)
			skipCount++
			continue
		}

		log.Printf("📥 [%d/%d] ID %s indiriliyor...", i+1, len(items), item.ID)

		html, err := s.fetcher.FetchDecisionHTML(item.ID)
		if err != nil {
			log.Printf("❌ ID %s indirilemedi: %v", item.ID, err)
			errorCount++
			time.Sleep(1 * time.Second)
			continue
		}

		decision := s.parser.ParseHTML(html, item.ID)

		// Use metadata from API - API data takes priority
		if item.Daire != "" {
			decision.Daire = item.Daire
		}
		if item.EsasNo != "" {
			decision.EsasNo = item.EsasNo
		}
		if item.KararNo != "" {
			decision.KararNo = item.KararNo
		}
		if item.KararTarihi != "" {
			decision.KararTarihi = item.KararTarihi
		}

		if err := s.db.SaveDecision(decision); err != nil {
			log.Printf("❌ ID %s kaydedilemedi: %v", item.ID, err)
			errorCount++
		} else {
			log.Printf("✅ ID %s kaydedildi (Daire: %s, Esas: %s, Karar: %s, Tarih: %s)",
				item.ID, decision.Daire, decision.EsasNo, decision.KararNo, decision.KararTarihi)
			successCount++

			// Queue decision for embedding generation if embedding manager exists
			if s.embeddingManager != nil {
				s.embeddingManager.QueueDecision(*decision)
			}
		}

		time.Sleep(500 * time.Millisecond)
	}

	log.Printf("📊 Sayfa %d özeti: %d başarılı, %d atlandı, %d hata",
		pageNumber, successCount, skipCount, errorCount)

	return nil
}

func (s *Scraper) ScrapeAll(startPage, endPage, pageSize int) {
	log.Printf("🚀 Tarama başlatılıyor: Sayfa %d-%d (boyut: %d)", startPage, endPage, pageSize)

	for page := startPage; page <= endPage; page++ {
		select {
		case <-s.stopChan:
			log.Println("⏹️ Tarama durduruldu")
			return
		default:
		}

		if err := s.ProcessPage(page, pageSize); err != nil {
			log.Printf("❌ Sayfa %d işlenirken hata: %v", page, err)
		}

		if page < endPage {
			log.Printf("⏸️ 2 saniye bekleniyor...")
			time.Sleep(2 * time.Second)
		}
	}

	stats, _ := s.db.GetStats()
	log.Printf("✅ Tarama tamamlandı! Toplam karar: %v", stats["total_decisions"])
}

func (s *Scraper) StartHTTPServer(port string) {
	// Use Gin server
	log.Printf("🌐 Web Dashboard: http://localhost%s", port)
	log.Printf("   📊 API: http://localhost%s/api/decisions", port)
	log.Printf("   🧠 Semantic Search: http://localhost%s/api/semantic-search", port)
	log.Printf("   ❤️ Health: http://localhost%s/health", port)

	StartGinServer(s.db, port)
}

func (s *Scraper) Close() {
	if s.db != nil {
		s.db.Close()
	}
}

func main() {
	var (
		startPage  = flag.Int("start", 1, "Başlangıç sayfası")
		endPage    = flag.Int("end", 100, "Bitiş sayfası")
		pageSize   = flag.Int("size", 20, "Sayfa başına karar sayısı")
		httpPort   = flag.String("port", ":8020", "HTTP sunucu portu")
		noServer   = flag.Bool("no-server", false, "HTTP sunucusunu devre dışı bırak")
		serverOnly = flag.Bool("server-only", false, "Sadece HTTP sunucusunu çalıştır")
	)
	flag.Parse()

	// Check AUTO_SCRAPE environment variable
	autoScrape := os.Getenv("AUTO_SCRAPE") == "true" || os.Getenv("AUTO_SCRAPE") == "1"

	// Override flags if AUTO_SCRAPE is enabled
	if autoScrape {
		log.Println("🚀 AUTO_SCRAPE enabled - Otomatik scraping başlatılıyor...")

		// Get parameters from environment or use defaults
		if envStart := os.Getenv("SCRAPE_START"); envStart != "" {
			fmt.Sscanf(envStart, "%d", startPage)
		}
		if envEnd := os.Getenv("SCRAPE_END"); envEnd != "" {
			fmt.Sscanf(envEnd, "%d", endPage)
		} else {
			*endPage = 100000 // Default to large number for continuous scraping
		}
		if envSize := os.Getenv("SCRAPE_SIZE"); envSize != "" {
			fmt.Sscanf(envSize, "%d", pageSize)
		}

		// Force concurrent operation mode
		*serverOnly = false
		*noServer = false
	}

	scraper, err := NewScraper()
	if err != nil {
		log.Fatal(err)
	}
	defer scraper.Close()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)

	if *serverOnly && !autoScrape {
		go scraper.StartHTTPServer(*httpPort)
		stats, _ := scraper.db.GetStats()
		log.Printf("📊 Mevcut veritabanı: %v karar", stats["total_decisions"])
		<-sigChan
		log.Println("\n🛑 Sunucu kapatılıyor...")
		return
	}

	go func() {
		<-sigChan
		log.Println("\n🛑 Kapatma sinyali alındı...")
		close(scraper.stopChan)
	}()

	// Always start HTTP server when AUTO_SCRAPE is enabled or when not in no-server mode
	if autoScrape || !*noServer {
		go scraper.StartHTTPServer(*httpPort)
	}

	// If AUTO_SCRAPE is enabled, run scraper in a goroutine
	if autoScrape {
		go func() {
			log.Printf("📊 Scraping parameters: Start=%d, End=%d, Size=%d", *startPage, *endPage, *pageSize)

			// Use adaptive scraper for AUTO_SCRAPE
			adaptiveScraper := NewAdaptiveScraper(scraper.db)

			// CRITICAL: Share the embedding manager with adaptive scraper
			adaptiveScraper.embeddingManager = scraper.embeddingManager

			ctx := context.Background()

			if err := adaptiveScraper.ProcessWithAdaptiveRate(ctx, *startPage, *endPage, *pageSize); err != nil {
				log.Printf("❌ Auto-scraping hatası: %v", err)
			} else {
				finalStats := adaptiveScraper.GetAdaptiveStats()
				log.Printf("✅ Auto-scraping tamamlandı! %d istek, %d başarılı",
					finalStats["total_requests"], finalStats["total_success"])
			}
		}()

		// Keep the main process running
		<-sigChan
		log.Println("\n🛑 Kapatma sinyali alındı...")
		return
	}

	// Always use adaptive scraper (it's smart and safe)
	log.Printf("🛡️ Adaptive scraper başlatılıyor...")
	adaptiveScraper := NewAdaptiveScraper(scraper.db)

	// Share the embedding manager with adaptive scraper
	adaptiveScraper.embeddingManager = scraper.embeddingManager

	ctx := context.Background()
	if err := adaptiveScraper.ProcessWithAdaptiveRate(ctx, *startPage, *endPage, *pageSize); err != nil {
		log.Printf("❌ Scraping hatası: %v", err)
	}

	// Final stats
	finalStats := adaptiveScraper.GetAdaptiveStats()
	log.Printf("✅ Tamamlandı! %d istek, %d başarılı",
		finalStats["total_requests"], finalStats["total_success"])
}
