package main

import (
	"embed"
	"encoding/json"
	"io/fs"
	"log"
	"net/http"
	"strconv"
	"strings"
)

//go:embed static/*
var staticFiles embed.FS

type WebServer struct {
	db               *Database
	embeddingManager *EmbeddingManager
}

func NewWebServer(db *Database) *WebServer {
	return &WebServer{
		db:               db,
		embeddingManager: NewEmbeddingManager(db),
	}
}

func (w *WebServer) RegisterRoutes(mux *http.ServeMux) {
	// Debug log
	log.Printf("🔧 Registering routes, embeddingManager: %v", w.embeddingManager != nil)

	// API endpoints - MUST be registered before static files!
	// Note: /api/semantic-search is registered in main.go
	mux.HandleFunc("/api/embedding/health", w.handleEmbeddingHealth)
	log.Printf("✅ Registered /api/embedding/health")
	mux.HandleFunc("/api/embedding/process-batch", w.handleProcessBatch)
	log.Printf("✅ Registered /api/embedding/process-batch")
	mux.HandleFunc("/api/stats", w.handleStats)
	mux.HandleFunc("/api/search", w.handleSearch)
	mux.HandleFunc("/api/daires", w.handleDaires)
	mux.HandleFunc("/api/decisions/", w.handleDecisionDetail)  // This catches /api/decisions/* 
	mux.HandleFunc("/api/decisions", w.handleDecisions)
	
	// Static files - MUST be last!
	staticFS, _ := fs.Sub(staticFiles, "static")
	mux.Handle("/", http.FileServer(http.FS(staticFS)))
}

func (w *WebServer) handleStats(wr http.ResponseWriter, r *http.Request) {
	stats, err := w.db.GetStats()
	if err != nil {
		http.Error(wr, err.Error(), http.StatusInternalServerError)
		return
	}
	
	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(stats)
}

func (w *WebServer) handleDecisions(wr http.ResponseWriter, r *http.Request) {
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page < 1 {
		page = 1
	}
	
	limit, _ := strconv.Atoi(r.URL.Query().Get("limit"))
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	// Basic filters
	search := r.URL.Query().Get("search")
	daire := r.URL.Query().Get("daire")
	
	// Advanced filters
	filters := map[string]interface{}{
		"kurullar":       r.URL.Query()["kurullar[]"],
		"hukukDaireler":  r.URL.Query()["hukukDaireler[]"],
		"cezaDaireler":   r.URL.Query()["cezaDaireler[]"],
		"esasYil":        r.URL.Query().Get("esasYil"),
		"esasIlkSiraNo":  r.URL.Query().Get("esasIlkSiraNo"),
		"esasSonSiraNo":  r.URL.Query().Get("esasSonSiraNo"),
		"kararYil":       r.URL.Query().Get("kararYil"),
		"kararIlkSiraNo": r.URL.Query().Get("kararIlkSiraNo"),
		"kararSonSiraNo": r.URL.Query().Get("kararSonSiraNo"),
		"baslangicTarihi": r.URL.Query().Get("baslangicTarihi"),
		"bitisTarihi":     r.URL.Query().Get("bitisTarihi"),
		"sortBy":          r.URL.Query().Get("sortBy"),
		"sortOrder":       r.URL.Query().Get("sortOrder"),
	}
	
	decisions, total, err := w.db.GetDecisionsAdvanced(page, limit, search, daire, filters)
	if err != nil {
		http.Error(wr, err.Error(), http.StatusInternalServerError)
		return
	}
	
	totalPages := (total + limit - 1) / limit
	
	response := map[string]interface{}{
		"decisions":   decisions,
		"page":        page,
		"limit":       limit,
		"total":       total,
		"total_pages": totalPages,
	}
	
	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(response)
}

func (w *WebServer) handleDecisionDetail(wr http.ResponseWriter, r *http.Request) {
	// Extract ID from path
	path := strings.TrimPrefix(r.URL.Path, "/api/decisions/")
	if path == "" {
		http.Error(wr, "Decision ID required", http.StatusBadRequest)
		return
	}
	
	decision, err := w.db.GetDecisionByID(path)
	if err != nil {
		http.Error(wr, "Decision not found", http.StatusNotFound)
		return
	}
	
	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(decision)
}

func (w *WebServer) handleDaires(wr http.ResponseWriter, r *http.Request) {
	daires, err := w.db.GetDaires()
	if err != nil {
		http.Error(wr, err.Error(), http.StatusInternalServerError)
		return
	}
	
	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(daires)
}

func (w *WebServer) handleSearch(wr http.ResponseWriter, r *http.Request) {
	query := r.URL.Query().Get("q")
	if query == "" {
		http.Error(wr, "Query parameter required", http.StatusBadRequest)
		return
	}
	
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page < 1 {
		page = 1
	}
	
	results, total, err := w.db.SearchDecisions(query, page, 10)
	if err != nil {
		http.Error(wr, err.Error(), http.StatusInternalServerError)
		return
	}
	
	response := map[string]interface{}{
		"results": results,
		"query":   query,
		"page":    page,
		"total":   total,
	}
	
	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(response)
}

func (w *WebServer) handleSemanticSearch(wr http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(wr, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	query := r.URL.Query().Get("q")
	if query == "" {
		http.Error(wr, "Query parameter 'q' required", http.StatusBadRequest)
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 10 // Default limit
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 50 {
			limit = parsedLimit
		}
	}

	// Perform semantic search
	results, err := w.embeddingManager.SemanticSearch(query, limit)
	if err != nil {
		if strings.Contains(err.Error(), "embedding service") {
			http.Error(wr, "Embedding service unavailable", http.StatusServiceUnavailable)
		} else {
			http.Error(wr, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	response := map[string]interface{}{
		"results": results,
		"query":   query,
		"count":   len(results),
		"type":    "semantic_search",
		"model":   "BGE-M3",
	}

	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(response)
}

func (w *WebServer) handleEmbeddingHealth(wr http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(wr, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	err := w.embeddingManager.CheckServiceHealth()
	if err != nil {
		response := map[string]interface{}{
			"status": "unhealthy",
			"error":  err.Error(),
		}
		wr.Header().Set("Content-Type", "application/json")
		wr.WriteHeader(http.StatusServiceUnavailable)
		json.NewEncoder(wr).Encode(response)
		return
	}

	response := map[string]interface{}{
		"status": "healthy",
		"model":  "BGE-M3",
	}

	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(response)
}

func (w *WebServer) handleProcessBatch(wr http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(wr, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 100 // Default batch size
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 1000 {
			limit = parsedLimit
		}
	}

	// Process batch embeddings
	err := w.embeddingManager.ProcessBatchEmbeddings(limit)
	if err != nil {
		if strings.Contains(err.Error(), "embedding service") {
			http.Error(wr, "Embedding service unavailable", http.StatusServiceUnavailable)
		} else {
			http.Error(wr, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	response := map[string]interface{}{
		"status":    "completed",
		"processed": limit,
		"message":   "Batch embedding processing completed",
	}

	wr.Header().Set("Content-Type", "application/json")
	json.NewEncoder(wr).Encode(response)
}