package main

import (
	"database/sql"
	"fmt"
	"log"
	"sync"
	"time"
)

// OptimizedDatabase wraps the database with performance optimizations
type OptimizedDatabase struct {
	db         *sql.DB
	writeMutex sync.Mutex
	batchQueue []Decision
	batchSize  int
	flushTimer *time.Timer
}

// NewOptimizedDatabase creates a database optimized for concurrent access
func NewOptimizedDatabase(dbPath string) (*OptimizedDatabase, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	// Apply optimizations
	pragmas := []string{
		"PRAGMA journal_mode = WAL",           // Write-Ahead Logging for better concurrency
		"PRAGMA synchronous = NORMAL",         // Faster writes, still safe
		"PRAGMA cache_size = 10000",          // 10MB cache
		"PRAGMA temp_store = MEMORY",         // Use memory for temp tables
		"PRAGMA mmap_size = 30000000000",     // 30GB memory-mapped I/O
		"PRAGMA busy_timeout = 5000",         // 5 second timeout for locks
		"PRAGMA wal_autocheckpoint = 1000",   // Checkpoint every 1000 pages
	}

	for _, pragma := range pragmas {
		if _, err := db.Exec(pragma); err != nil {
			log.Printf("Warning: Failed to set %s: %v", pragma, err)
		}
	}

	// Create table with optimized schema
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS decisions (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		decision_id TEXT UNIQUE NOT NULL,
		daire TEXT NOT NULL,
		esas_no TEXT,
		karar_no TEXT,
		karar_tarihi TEXT,
		mahkeme_adi TEXT,
		ilam_tarihi TEXT,
		decision_text TEXT,
		raw_html TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		worker_id TEXT,
		batch_id INTEGER,
		processing_time_ms INTEGER
	);

	-- Indexes for fast lookups
	CREATE INDEX IF NOT EXISTS idx_decision_id ON decisions(decision_id);
	CREATE INDEX IF NOT EXISTS idx_daire ON decisions(daire);
	CREATE INDEX IF NOT EXISTS idx_karar_tarihi ON decisions(karar_tarihi);
	CREATE INDEX IF NOT EXISTS idx_created_at ON decisions(created_at);
	CREATE INDEX IF NOT EXISTS idx_worker_id ON decisions(worker_id);
	CREATE INDEX IF NOT EXISTS idx_batch_id ON decisions(batch_id);

	-- Full-text search table
	CREATE VIRTUAL TABLE IF NOT EXISTS decisions_fts USING fts5(
		decision_id,
		esas_no,
		karar_no,
		decision_text,
		content=decisions,
		content_rowid=id
	);

	-- Triggers to keep FTS in sync
	CREATE TRIGGER IF NOT EXISTS decisions_ai AFTER INSERT ON decisions BEGIN
		INSERT INTO decisions_fts(rowid, decision_id, esas_no, karar_no, decision_text)
		VALUES (new.id, new.decision_id, new.esas_no, new.karar_no, new.decision_text);
	END;

	-- Metadata table for tracking progress
	CREATE TABLE IF NOT EXISTS scraping_metadata (
		id INTEGER PRIMARY KEY,
		key TEXT UNIQUE NOT NULL,
		value TEXT,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);

	-- Failed pages tracking
	CREATE TABLE IF NOT EXISTS failed_pages (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		page_number INTEGER NOT NULL,
		page_size INTEGER,
		error_message TEXT,
		retry_count INTEGER DEFAULT 0,
		worker_id TEXT,
		failed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(page_number, page_size)
	);
	`

	if _, err := db.Exec(createTableSQL); err != nil {
		return nil, fmt.Errorf("failed to create tables: %w", err)
	}

	return &OptimizedDatabase{
		db:        db,
		batchSize: 100,
		batchQueue: make([]Decision, 0, 100),
	}, nil
}

// BatchInsert accumulates decisions and inserts them in batches
func (od *OptimizedDatabase) BatchInsert(decision Decision, workerID string, batchID int) error {
	od.writeMutex.Lock()
	defer od.writeMutex.Unlock()

	decision.WorkerID = workerID
	decision.BatchID = batchID
	od.batchQueue = append(od.batchQueue, decision)

	// Flush if batch is full
	if len(od.batchQueue) >= od.batchSize {
		return od.FlushBatch()
	}

	// Set flush timer if not already set
	if od.flushTimer == nil {
		od.flushTimer = time.AfterFunc(5*time.Second, func() {
			od.writeMutex.Lock()
			defer od.writeMutex.Unlock()
			od.FlushBatch()
		})
	}

	return nil
}

// FlushBatch writes all queued decisions to database
func (od *OptimizedDatabase) FlushBatch() error {
	if len(od.batchQueue) == 0 {
		return nil
	}

	tx, err := od.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	stmt, err := tx.Prepare(`
		INSERT OR IGNORE INTO decisions (
			decision_id, daire, esas_no, karar_no, karar_tarihi,
			mahkeme_adi, ilam_tarihi, decision_text, raw_html,
			worker_id, batch_id, processing_time_ms
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`)
	if err != nil {
		return err
	}
	defer stmt.Close()

	for _, decision := range od.batchQueue {
		_, err = stmt.Exec(
			decision.ID,
			decision.Daire,
			decision.EsasNo,
			decision.KararNo,
			decision.KararTarihi,
			decision.MahkemeAdi,
			decision.IlamTarihi,
			decision.DecisionText,
			decision.RawHTML,
			decision.WorkerID,
			decision.BatchID,
			decision.ProcessingTimeMs,
		)
		if err != nil {
			log.Printf("Failed to insert decision %s: %v", decision.ID, err)
		}
	}

	if err := tx.Commit(); err != nil {
		return err
	}

	log.Printf("Flushed batch of %d decisions", len(od.batchQueue))
	od.batchQueue = od.batchQueue[:0]

	if od.flushTimer != nil {
		od.flushTimer.Stop()
		od.flushTimer = nil
	}

	return nil
}

// GetProgress returns current scraping progress
func (od *OptimizedDatabase) GetProgress() (map[string]interface{}, error) {
	progress := make(map[string]interface{})

	// Total decisions
	var totalDecisions int
	err := od.db.QueryRow("SELECT COUNT(*) FROM decisions").Scan(&totalDecisions)
	if err != nil {
		return nil, err
	}
	progress["total_decisions"] = totalDecisions

	// Decisions by worker
	rows, err := od.db.Query(`
		SELECT worker_id, COUNT(*) as count 
		FROM decisions 
		WHERE worker_id IS NOT NULL 
		GROUP BY worker_id
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	workerStats := make(map[string]int)
	for rows.Next() {
		var workerID string
		var count int
		rows.Scan(&workerID, &count)
		workerStats[workerID] = count
	}
	progress["worker_stats"] = workerStats

	// Failed pages
	var failedCount int
	err = od.db.QueryRow("SELECT COUNT(*) FROM failed_pages").Scan(&failedCount)
	if err != nil {
		return nil, err
	}
	progress["failed_pages"] = failedCount

	// Recent rate
	var recentCount int
	err = od.db.QueryRow(`
		SELECT COUNT(*) FROM decisions 
		WHERE created_at > datetime('now', '-1 hour')
	`).Scan(&recentCount)
	if err != nil {
		return nil, err
	}
	progress["hourly_rate"] = recentCount

	// Average processing time
	var avgTime sql.NullFloat64
	err = od.db.QueryRow(`
		SELECT AVG(processing_time_ms) FROM decisions 
		WHERE processing_time_ms > 0
	`).Scan(&avgTime)
	if err == nil && avgTime.Valid {
		progress["avg_processing_ms"] = avgTime.Float64
	}

	return progress, nil
}

// RecordFailedPage records a page that failed to process
func (od *OptimizedDatabase) RecordFailedPage(pageNumber, pageSize int, errorMsg, workerID string) error {
	_, err := od.db.Exec(`
		INSERT OR REPLACE INTO failed_pages (page_number, page_size, error_message, worker_id, retry_count)
		VALUES (?, ?, ?, ?, 
			COALESCE((SELECT retry_count + 1 FROM failed_pages WHERE page_number = ? AND page_size = ?), 0)
		)
	`, pageNumber, pageSize, errorMsg, workerID, pageNumber, pageSize)
	return err
}

// GetFailedPages returns pages that need to be retried
func (od *OptimizedDatabase) GetFailedPages(maxRetries int) ([]FailedPage, error) {
	rows, err := od.db.Query(`
		SELECT page_number, page_size, error_message, retry_count, worker_id, failed_at
		FROM failed_pages
		WHERE retry_count < ?
		ORDER BY retry_count ASC, failed_at ASC
	`, maxRetries)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var pages []FailedPage
	for rows.Next() {
		var page FailedPage
		err := rows.Scan(&page.PageNumber, &page.PageSize, &page.ErrorMessage, 
			&page.RetryCount, &page.WorkerID, &page.FailedAt)
		if err != nil {
			continue
		}
		pages = append(pages, page)
	}

	return pages, nil
}

// SetMetadata stores key-value metadata
func (od *OptimizedDatabase) SetMetadata(key, value string) error {
	_, err := od.db.Exec(`
		INSERT OR REPLACE INTO scraping_metadata (key, value, updated_at)
		VALUES (?, ?, CURRENT_TIMESTAMP)
	`, key, value)
	return err
}

// GetMetadata retrieves metadata value
func (od *OptimizedDatabase) GetMetadata(key string) (string, error) {
	var value string
	err := od.db.QueryRow("SELECT value FROM scraping_metadata WHERE key = ?", key).Scan(&value)
	return value, err
}

// Checkpoint forces a WAL checkpoint
func (od *OptimizedDatabase) Checkpoint() error {
	_, err := od.db.Exec("PRAGMA wal_checkpoint(TRUNCATE)")
	return err
}

// Vacuum optimizes the database file
func (od *OptimizedDatabase) Vacuum() error {
	_, err := od.db.Exec("VACUUM")
	return err
}

// AnalyzeIndexes updates index statistics
func (od *OptimizedDatabase) AnalyzeIndexes() error {
	_, err := od.db.Exec("ANALYZE")
	return err
}

// Close flushes pending batches and closes the database
func (od *OptimizedDatabase) Close() error {
	od.writeMutex.Lock()
	defer od.writeMutex.Unlock()

	// Flush any pending batches
	if err := od.FlushBatch(); err != nil {
		log.Printf("Error flushing final batch: %v", err)
	}

	// Checkpoint WAL
	od.Checkpoint()

	return od.db.Close()
}

// Decision extended with metadata
type DecisionExtended struct {
	Decision
	WorkerID         string
	BatchID          int
	ProcessingTimeMs int64
}

// FailedPage represents a page that failed to process
type FailedPage struct {
	PageNumber   int
	PageSize     int
	ErrorMessage string
	RetryCount   int
	WorkerID     string
	FailedAt     time.Time
}