package main

import (
	"database/sql"
	"fmt"
	"sync"
	"testing"
	"time"
)

// TestDatabaseSchema verifies the database schema is correct
func TestDatabaseSchema(t *testing.T) {
	db, err := NewDatabase("data/test_decisions.db")
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// Check table exists
	var tableName string
	err = db.db.QueryRow("SELECT name FROM sqlite_master WHERE type='table' AND name='decisions'").Scan(&tableName)
	if err != nil {
		t.Errorf("decisions table doesn't exist: %v", err)
	}

	// Check columns
	expectedColumns := map[string]string{
		"id":            "INTEGER",
		"decision_id":   "TEXT",
		"daire":         "TEXT",
		"esas_no":       "TEXT",
		"karar_no":      "TEXT",
		"karar_tarihi":  "TEXT",
		"decision_text": "TEXT",
		"raw_html":      "TEXT",
		"created_at":    "DATETIME",
	}

	rows, err := db.db.Query("PRAGMA table_info(decisions)")
	if err != nil {
		t.Fatalf("Failed to get table info: %v", err)
	}
	defer rows.Close()

	actualColumns := make(map[string]string)
	for rows.Next() {
		var cid int
		var name, dtype string
		var notnull int
		var dflt sql.NullString
		var pk int
		
		err := rows.Scan(&cid, &name, &dtype, &notnull, &dflt, &pk)
		if err != nil {
			t.Errorf("Failed to scan column info: %v", err)
		}
		actualColumns[name] = dtype
	}

	for col, expectedType := range expectedColumns {
		if actualType, exists := actualColumns[col]; !exists {
			t.Errorf("Column %s doesn't exist", col)
		} else if actualType != expectedType {
			t.Errorf("Column %s has wrong type: expected %s, got %s", col, expectedType, actualType)
		}
	}
}

// TestConcurrentInserts tests multiple workers inserting simultaneously
func TestConcurrentInserts(t *testing.T) {
	db, err := NewDatabase("data/test_concurrent.db")
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// Enable WAL mode for better concurrency
	db.db.Exec("PRAGMA journal_mode=WAL")
	db.db.Exec("PRAGMA busy_timeout=5000")

	numWorkers := 10
	decisionsPerWorker := 100
	var wg sync.WaitGroup
	errors := make(chan error, numWorkers*decisionsPerWorker)

	start := time.Now()

	for w := 0; w < numWorkers; w++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			
			for i := 0; i < decisionsPerWorker; i++ {
				decision := Decision{
					ID:           fmt.Sprintf("worker_%d_decision_%d", workerID, i),
					Daire:        fmt.Sprintf("%d. Test Dairesi", workerID),
					EsasNo:       fmt.Sprintf("2024/%d", workerID*1000+i),
					KararNo:      fmt.Sprintf("2024/%d", workerID*2000+i),
					KararTarihi:  "2024-01-01",
					DecisionText: fmt.Sprintf("Test decision from worker %d", workerID),
					RawHTML:      "<html>test</html>",
				}
				
				if err := db.SaveDecision(&decision); err != nil {
					errors <- fmt.Errorf("Worker %d failed on decision %d: %v", workerID, i, err)
				}
			}
		}(w)
	}

	wg.Wait()
	close(errors)

	elapsed := time.Since(start)
	
	// Check for errors
	var errorCount int
	for err := range errors {
		t.Errorf("Insert error: %v", err)
		errorCount++
	}

	// Verify all records were inserted
	var count int
	err = db.db.QueryRow("SELECT COUNT(*) FROM decisions").Scan(&count)
	if err != nil {
		t.Fatalf("Failed to count records: %v", err)
	}

	expectedCount := numWorkers * decisionsPerWorker
	if count != expectedCount {
		t.Errorf("Expected %d records, got %d", expectedCount, count)
	}

	t.Logf("Inserted %d records in %v (%.0f records/sec) with %d errors", 
		count, elapsed, float64(count)/elapsed.Seconds(), errorCount)
}

// TestDuplicateHandling tests that duplicate decisions are handled correctly
func TestDuplicateHandling(t *testing.T) {
	db, err := NewDatabase("data/test_duplicates.db")
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	decision := Decision{
		ID:           "duplicate_test_123",
		Daire:        "Test Dairesi",
		EsasNo:       "2024/1",
		KararNo:      "2024/1",
		KararTarihi:  "2024-01-01",
		DecisionText: "Original decision",
	}

	// First insert should succeed
	err = db.SaveDecision(&decision)
	if err != nil {
		t.Errorf("First insert failed: %v", err)
	}

	// Check it exists
	exists, err := db.IsDecisionExists(decision.ID)
	if err != nil {
		t.Errorf("Failed to check existence: %v", err)
	}
	if !exists {
		t.Error("Decision should exist after insert")
	}

	// Second insert should be handled gracefully
	decision.DecisionText = "Modified decision"
	err = db.SaveDecision(&decision)
	if err == nil {
		t.Error("Expected error on duplicate insert, got nil")
	}

	// Verify original data wasn't modified
	var savedText string
	err = db.db.QueryRow("SELECT decision_text FROM decisions WHERE decision_id = ?", decision.ID).Scan(&savedText)
	if err != nil {
		t.Errorf("Failed to query saved decision: %v", err)
	}
	if savedText != "Original decision" {
		t.Errorf("Decision was modified: expected 'Original decision', got '%s'", savedText)
	}
}

// TestTransactionRollback tests that failed transactions don't corrupt data
func TestTransactionRollback(t *testing.T) {
	db, err := NewDatabase("data/test_transactions.db")
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// Count initial records
	var initialCount int
	db.db.QueryRow("SELECT COUNT(*) FROM decisions").Scan(&initialCount)

	// Start a transaction
	tx, err := db.db.Begin()
	if err != nil {
		t.Fatalf("Failed to begin transaction: %v", err)
	}

	// Insert some records
	for i := 0; i < 5; i++ {
		_, err = tx.Exec(`
			INSERT INTO decisions (decision_id, daire, esas_no, karar_no, decision_text)
			VALUES (?, ?, ?, ?, ?)`,
			fmt.Sprintf("tx_test_%d", i),
			"Test Daire",
			fmt.Sprintf("2024/%d", i),
			fmt.Sprintf("2024/%d", i),
			"Test decision",
		)
		if err != nil {
			t.Errorf("Failed to insert in transaction: %v", err)
		}
	}

	// Rollback the transaction
	tx.Rollback()

	// Verify no records were added
	var finalCount int
	db.db.QueryRow("SELECT COUNT(*) FROM decisions").Scan(&finalCount)
	
	if finalCount != initialCount {
		t.Errorf("Transaction rollback failed: expected %d records, got %d", initialCount, finalCount)
	}
}

// TestSearchPerformance tests search query performance
func TestSearchPerformance(t *testing.T) {
	db, err := NewDatabase("data/test_performance.db")
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// Insert test data
	for i := 0; i < 10000; i++ {
		decision := Decision{
			ID:           fmt.Sprintf("perf_test_%d", i),
			Daire:        fmt.Sprintf("%d. Hukuk Dairesi", (i%23)+1),
			EsasNo:       fmt.Sprintf("2024/%d", i),
			KararNo:      fmt.Sprintf("2024/%d", i*2),
			DecisionText: fmt.Sprintf("Karar metni %d kira sözleşmesi tazminat", i),
		}
		db.SaveDecision(&decision)
	}

	// Test search performance
	searchTerms := []string{"kira", "tazminat", "sözleşme", "2024"}
	
	for _, term := range searchTerms {
		start := time.Now()
		
		decisions, total, err := db.SearchDecisions(term, 1, 20)
		if err != nil {
			t.Errorf("Search failed for '%s': %v", term, err)
		}
		
		elapsed := time.Since(start)
		if elapsed > 100*time.Millisecond {
			t.Errorf("Search for '%s' too slow: %v", term, elapsed)
		}
		
		t.Logf("Search for '%s': %d results (total: %d) in %v", term, len(decisions), total, elapsed)
	}
}

// TestAPIResponseParsing tests different API response formats
func TestAPIResponseParsing(t *testing.T) {
	testCases := []struct {
		name        string
		response    string
		shouldError bool
	}{
		{
			name: "JSON Response",
			response: `{"data": "<html><body>Test content</body></html>"}`,
			shouldError: false,
		},
		{
			name: "XML Wrapped HTML",
			response: `<AdaletResponseDto><data>&lt;html&gt;&lt;body&gt;Test content&lt;/body&gt;&lt;/html&gt;</data></AdaletResponseDto>`,
			shouldError: false,
		},
		{
			name: "Plain HTML",
			response: `<html><body>Test content</body></html>`,
			shouldError: false,
		},
		{
			name: "Empty Response",
			response: `{"data": ""}`,
			shouldError: true,
		},
		{
			name: "Invalid JSON",
			response: `{invalid json}`,
			shouldError: true,
		},
	}

	// fetcher := NewFetcher()
	// parser := NewParser()

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// This would need to be refactored to test the actual parsing logic
			// For now, we're testing the concept
			if tc.shouldError && tc.response != "" {
				// Expect error
			} else if !tc.shouldError && tc.response == "" {
				t.Errorf("Expected success but got empty response")
			}
		})
	}
}

// TestBatchInsertPerformance tests batch vs individual inserts
func TestBatchInsertPerformance(t *testing.T) {
	// Individual inserts
	db1, _ := NewDatabase("data/test_individual.db")
	defer db1.Close()

	start := time.Now()
	for i := 0; i < 1000; i++ {
		decision := Decision{
			ID:           fmt.Sprintf("individual_%d", i),
			Daire:        "Test",
			DecisionText: "Test",
		}
		db1.SaveDecision(&decision)
	}
	individualTime := time.Since(start)

	// Batch inserts (simulated with transaction)
	db2, _ := NewDatabase("data/test_batch.db")
	defer db2.Close()

	start = time.Now()
	tx, _ := db2.db.Begin()
	for i := 0; i < 1000; i++ {
		tx.Exec(`INSERT INTO decisions (decision_id, daire, decision_text) VALUES (?, ?, ?)`,
			fmt.Sprintf("batch_%d", i), "Test", "Test")
	}
	tx.Commit()
	batchTime := time.Since(start)

	t.Logf("Individual inserts: %v", individualTime)
	t.Logf("Batch inserts: %v", batchTime)
	t.Logf("Speedup: %.2fx", individualTime.Seconds()/batchTime.Seconds())
}

// TestDatabaseIndexes tests that proper indexes exist
func TestDatabaseIndexes(t *testing.T) {
	db, err := NewDatabase("data/test_indexes.db")
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// Create indexes
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_decision_id ON decisions(decision_id)",
		"CREATE INDEX IF NOT EXISTS idx_daire ON decisions(daire)",
		"CREATE INDEX IF NOT EXISTS idx_karar_tarihi ON decisions(karar_tarihi)",
		"CREATE INDEX IF NOT EXISTS idx_created_at ON decisions(created_at)",
	}

	for _, idx := range indexes {
		if _, err := db.db.Exec(idx); err != nil {
			t.Errorf("Failed to create index: %v", err)
		}
	}

	// Verify indexes exist
	rows, err := db.db.Query("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
	if err != nil {
		t.Fatalf("Failed to query indexes: %v", err)
	}
	defer rows.Close()

	var indexCount int
	for rows.Next() {
		var name string
		rows.Scan(&name)
		indexCount++
		t.Logf("Found index: %s", name)
	}

	if indexCount < 4 {
		t.Errorf("Expected at least 4 indexes, found %d", indexCount)
	}
}

// TestMemoryUsage tests database memory consumption
func TestMemoryUsage(t *testing.T) {
	db, err := NewDatabase("data/test_memory.db")
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// Set memory optimizations
	db.db.Exec("PRAGMA cache_size = 10000")
	db.db.Exec("PRAGMA temp_store = MEMORY")
	db.db.Exec("PRAGMA mmap_size = 30000000000")

	// Check current settings
	var cacheSize int
	db.db.QueryRow("PRAGMA cache_size").Scan(&cacheSize)
	t.Logf("Cache size: %d pages", cacheSize)

	var pageSize int
	db.db.QueryRow("PRAGMA page_size").Scan(&pageSize)
	t.Logf("Page size: %d bytes", pageSize)

	var walMode string
	db.db.QueryRow("PRAGMA journal_mode").Scan(&walMode)
	t.Logf("Journal mode: %s", walMode)
}