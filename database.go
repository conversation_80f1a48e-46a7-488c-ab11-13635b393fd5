package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/lib/pq"
)

type Database struct {
	db *sql.DB
}

func NewDatabase() (*Database, error) {
	// Get connection parameters from environment
	host := os.Getenv("POSTGRES_HOST")
	if host == "" {
		host = "localhost"
	}
	port := os.Getenv("POSTGRES_PORT")
	if port == "" {
		port = "5432"
	}
	user := os.Getenv("POSTGRES_USER")
	if user == "" {
		user = "hukukat"
	}
	password := os.Getenv("POSTGRES_PASSWORD")
	if password == "" {
		password = "hukukat_secret_2024"
	}
	dbname := os.Getenv("POSTGRES_DB")
	if dbname == "" {
		dbname = "hukukat_db"
	}

	// Build connection string
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	// Connect to database
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("PostgreSQL bağlantısı açılamadı: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("PostgreSQL bağlantısı kurulamadı: %w", err)
	}

	database := &Database{db: db}
	if err := database.createTables(); err != nil {
		return nil, fmt.Errorf("tablolar oluşturulamadı: %w", err)
	}

	log.Println("✅ PostgreSQL veritabanı hazır")
	return database, nil
}

func (d *Database) createTables() error {
	// Enable pgvector extension
	if _, err := d.db.Exec("CREATE EXTENSION IF NOT EXISTS vector"); err != nil {
		return fmt.Errorf("failed to create vector extension: %w", err)
	}

	// Create decisions table (PostgreSQL syntax)
	_, err := d.db.Exec(`
		CREATE TABLE IF NOT EXISTS decisions (
			id SERIAL PRIMARY KEY,
			decision_id TEXT UNIQUE NOT NULL,
			daire TEXT,
			esas_no TEXT,
			karar_no TEXT,
			karar_tarihi TEXT,
			mahkeme_adi TEXT,
			ilam_tarihi TEXT,
			decision_text TEXT,
			raw_html TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create decisions table: %w", err)
	}

	// Create embeddings table for vector search
	_, err = d.db.Exec(`
		CREATE TABLE IF NOT EXISTS decision_embeddings (
			id SERIAL PRIMARY KEY,
			decision_id TEXT UNIQUE NOT NULL REFERENCES decisions(decision_id) ON DELETE CASCADE,
			embedding vector(1024),  -- BGE-M3 dimension
			embedding_model TEXT DEFAULT 'BGE-M3',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create embeddings table: %w", err)
	}

	// Create indexes
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_decisions_decision_id ON decisions(decision_id)",
		"CREATE INDEX IF NOT EXISTS idx_decisions_daire ON decisions(daire)",
		"CREATE INDEX IF NOT EXISTS idx_decisions_karar_tarihi ON decisions(karar_tarihi)",
		"CREATE INDEX IF NOT EXISTS idx_decisions_created_at ON decisions(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_embeddings_decision_id ON decision_embeddings(decision_id)",
		// Vector similarity search index (IVFFlat)
		"CREATE INDEX IF NOT EXISTS idx_embeddings_vector ON decision_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100)",
	}

	for _, idx := range indexes {
		if _, err := d.db.Exec(idx); err != nil {
			log.Printf("Warning: Failed to create index: %v", err)
		}
	}

	return nil
}

// SaveEmbedding saves an embedding vector for a decision
func (d *Database) SaveEmbedding(decisionID string, embedding []float32, model string) error {
	// Convert []float32 to pgvector format string
	embeddingStr := "["
	for i, v := range embedding {
		if i > 0 {
			embeddingStr += ","
		}
		embeddingStr += fmt.Sprintf("%f", v)
	}
	embeddingStr += "]"
	
	query := `
		INSERT INTO decision_embeddings (decision_id, embedding, embedding_model)
		VALUES ($1, $2::vector, $3)
		ON CONFLICT (decision_id) DO UPDATE SET
			embedding = EXCLUDED.embedding,
			embedding_model = EXCLUDED.embedding_model,
			created_at = CURRENT_TIMESTAMP
	`

	_, err := d.db.Exec(query, decisionID, embeddingStr, model)
	return err
}

// SearchSimilar finds similar decisions using vector similarity
func (d *Database) SearchSimilar(queryEmbedding []float32, limit int) ([]SimilarDecision, error) {
	// Convert []float32 to pgvector format string
	embeddingStr := "["
	for i, v := range queryEmbedding {
		if i > 0 {
			embeddingStr += ","
		}
		embeddingStr += fmt.Sprintf("%f", v)
	}
	embeddingStr += "]"
	
	query := `
		SELECT 
			d.decision_id,
			d.daire,
			d.esas_no,
			d.karar_no,
			d.karar_tarihi,
			d.decision_text,
			1 - (e.embedding <=> $1::vector) as similarity
		FROM decision_embeddings e
		JOIN decisions d ON d.decision_id = e.decision_id
		ORDER BY e.embedding <=> $1::vector
		LIMIT $2
	`

	rows, err := d.db.Query(query, embeddingStr, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []SimilarDecision
	for rows.Next() {
		var sd SimilarDecision
		err := rows.Scan(
			&sd.DecisionID,
			&sd.Daire,
			&sd.EsasNo,
			&sd.KararNo,
			&sd.KararTarihi,
			&sd.DecisionText,
			&sd.Similarity,
		)
		if err != nil {
			return nil, err
		}
		results = append(results, sd)
	}

	return results, nil
}

// GetDecisionsWithoutEmbeddings returns decisions that don't have embeddings yet
func (d *Database) GetDecisionsWithoutEmbeddings(limit int) ([]Decision, error) {
	query := `
		SELECT d.decision_id, d.daire, d.esas_no, d.karar_no, 
			d.karar_tarihi, d.mahkeme_adi, d.ilam_tarihi, d.decision_text
		FROM decisions d
		LEFT JOIN decision_embeddings e ON d.decision_id = e.decision_id
		WHERE e.id IS NULL
		LIMIT $1
	`

	rows, err := d.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var decisions []Decision
	for rows.Next() {
		var d Decision
		err := rows.Scan(
			&d.DecisionID,
			&d.Daire,
			&d.EsasNo,
			&d.KararNo,
			&d.KararTarihi,
			&d.MahkemeAdi,
			&d.IlamTarihi,
			&d.DecisionText,
		)
		if err != nil {
			return nil, err
		}
		decisions = append(decisions, d)
	}

	return decisions, nil
}

func (d *Database) SaveDecision(decision *Decision) error {
	query := `
		INSERT INTO decisions (decision_id, daire, esas_no, karar_no, karar_tarihi, mahkeme_adi, ilam_tarihi, decision_text, raw_html)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		ON CONFLICT(decision_id) DO UPDATE SET
			daire = EXCLUDED.daire,
			esas_no = EXCLUDED.esas_no,
			karar_no = EXCLUDED.karar_no,
			karar_tarihi = EXCLUDED.karar_tarihi,
			mahkeme_adi = EXCLUDED.mahkeme_adi,
			ilam_tarihi = EXCLUDED.ilam_tarihi,
			decision_text = EXCLUDED.decision_text,
			raw_html = EXCLUDED.raw_html
	`

	_, err := d.db.Exec(query,
		decision.DecisionID,
		decision.Daire,
		decision.EsasNo,
		decision.KararNo,
		decision.KararTarihi,
		decision.MahkemeAdi,
		decision.IlamTarihi,
		decision.DecisionText,
		decision.RawHTML,
	)

	if err != nil {
		return fmt.Errorf("karar kaydedilemedi: %w", err)
	}
	return nil
}

func (d *Database) IsDecisionExists(decisionID string) (bool, error) {
	var exists bool
	query := `SELECT EXISTS(SELECT 1 FROM decisions WHERE decision_id = $1)`
	err := d.db.QueryRow(query, decisionID).Scan(&exists)
	return exists, err
}

// CheckMultipleDecisions checks which decisions already exist in the database
// Returns a map where keys are decision IDs and values indicate existence
func (d *Database) CheckMultipleDecisions(decisionIDs []string) (map[string]bool, error) {
	if len(decisionIDs) == 0 {
		return map[string]bool{}, nil
	}

	// Build the query with placeholders
	query := `SELECT decision_id FROM decisions WHERE decision_id = ANY($1)`
	
	// Execute query - use pq.Array for PostgreSQL array type
	rows, err := d.db.Query(query, pq.Array(decisionIDs))
	if err != nil {
		return nil, fmt.Errorf("batch check failed: %w", err)
	}
	defer rows.Close()

	// Build result map
	existsMap := make(map[string]bool)
	// Initialize all as non-existent
	for _, id := range decisionIDs {
		existsMap[id] = false
	}
	
	// Mark existing ones as true
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			return nil, err
		}
		existsMap[id] = true
	}

	return existsMap, nil
}

func (d *Database) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	var totalCount int
	err := d.db.QueryRow("SELECT COUNT(*) FROM decisions").Scan(&totalCount)
	if err != nil {
		return nil, err
	}
	stats["total_decisions"] = totalCount

	var todayCount int
	err = d.db.QueryRow("SELECT COUNT(*) FROM decisions WHERE DATE(created_at) = CURRENT_DATE").Scan(&todayCount)
	if err != nil {
		return nil, err
	}
	stats["today_decisions"] = todayCount

	rows, err := d.db.Query(`
		SELECT daire, COUNT(*) as count 
		FROM decisions 
		WHERE daire IS NOT NULL AND daire != ''
		GROUP BY daire 
		ORDER BY count DESC 
		LIMIT 5
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	daireStats := []map[string]interface{}{}
	for rows.Next() {
		var daire string
		var count int
		if err := rows.Scan(&daire, &count); err != nil {
			continue
		}
		daireStats = append(daireStats, map[string]interface{}{
			"daire": daire,
			"count": count,
		})
	}
	stats["daire_distribution"] = daireStats
	stats["last_update"] = time.Now().Format("2006-01-02 15:04:05")

	return stats, nil
}

// GetEmbeddingStats returns embedding processing statistics
func (d *Database) GetEmbeddingStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total decisions with embeddings
	var withEmbeddings int
	err := d.db.QueryRow(`
		SELECT COUNT(DISTINCT d.decision_id) 
		FROM decisions d 
		INNER JOIN decision_embeddings e ON d.decision_id = e.decision_id
	`).Scan(&withEmbeddings)
	if err != nil {
		return nil, err
	}

	// Total decisions without embeddings
	var withoutEmbeddings int
	err = d.db.QueryRow(`
		SELECT COUNT(*) 
		FROM decisions d 
		LEFT JOIN decision_embeddings e ON d.decision_id = e.decision_id 
		WHERE e.id IS NULL
	`).Scan(&withoutEmbeddings)
	if err != nil {
		return nil, err
	}

	// Get total decisions count
	var totalDecisions int
	err = d.db.QueryRow("SELECT COUNT(*) FROM decisions").Scan(&totalDecisions)
	if err != nil {
		return nil, err
	}

	// Calculate percentage
	var percentage float64
	if totalDecisions > 0 {
		percentage = (float64(withEmbeddings) / float64(totalDecisions)) * 100
	}

	stats["processed"] = withEmbeddings
	stats["pending"] = withoutEmbeddings
	stats["total"] = totalDecisions
	stats["percentage"] = fmt.Sprintf("%.1f", percentage)

	return stats, nil
}

func (d *Database) GetDecisions(page, limit int, search, daire string) ([]map[string]interface{}, int, error) {
	return d.GetDecisionsAdvanced(page, limit, search, daire, nil)
}

func (d *Database) GetDecisionsAdvanced(page, limit int, search, daire string, filters map[string]interface{}) ([]map[string]interface{}, int, error) {
	offset := (page - 1) * limit
	var decisions []map[string]interface{}
	var total int
	
	// Build query with PostgreSQL syntax
	var args []interface{}
	var countArgs []interface{}
	paramCount := 0
	countParamCount := 0
	
	query := "SELECT decision_id, daire, esas_no, karar_no, karar_tarihi, SUBSTRING(decision_text, 1, 200) as decision_text FROM decisions WHERE 1=1"
	countQuery := "SELECT COUNT(*) FROM decisions WHERE 1=1"
	
	// Build WHERE clauses for advanced filters
	var daireConditions []string
	
	if search != "" {
		paramCount += 3
		countParamCount += 3
		query += " AND (decision_text ILIKE $1 OR esas_no ILIKE $2 OR karar_no ILIKE $3)"
		countQuery += " AND (decision_text ILIKE $1 OR esas_no ILIKE $2 OR karar_no ILIKE $3)"
		searchParam := "%" + search + "%"
		args = append(args, searchParam, searchParam, searchParam)
		countArgs = append(countArgs, searchParam, searchParam, searchParam)
	}
	
	// Handle simple daire filter or advanced daire filters
	if daire != "" {
		paramCount++
		countParamCount++
		query += fmt.Sprintf(" AND daire = $%d", paramCount)
		countQuery += fmt.Sprintf(" AND daire = $%d", countParamCount)
		args = append(args, daire)
		countArgs = append(countArgs, daire)
	} else if filters != nil {
		// Handle kurullar
		if kurullar, ok := filters["kurullar"].([]string); ok && len(kurullar) > 0 {
			for _, k := range kurullar {
				daireConditions = append(daireConditions, k)
			}
		}
		
		// Handle hukuk daireler
		if hukukDaireler, ok := filters["hukukDaireler"].([]string); ok && len(hukukDaireler) > 0 {
			for _, h := range hukukDaireler {
				daireConditions = append(daireConditions, h+". Hukuk Dairesi")
			}
		}
		
		// Handle ceza daireler
		if cezaDaireler, ok := filters["cezaDaireler"].([]string); ok && len(cezaDaireler) > 0 {
			for _, c := range cezaDaireler {
				daireConditions = append(daireConditions, c+". Ceza Dairesi")
			}
		}
		
		// Add daire conditions to query
		if len(daireConditions) > 0 {
			paramCount++
			countParamCount++
			query += fmt.Sprintf(" AND daire = ANY($%d)", paramCount)
			countQuery += fmt.Sprintf(" AND daire = ANY($%d)", countParamCount)
			args = append(args, pq.Array(daireConditions))
			countArgs = append(countArgs, pq.Array(daireConditions))
		}
		
		// Handle esas_no filters
		if esasYil, ok := filters["esasYil"].(string); ok && esasYil != "" {
			if esasIlk, ok := filters["esasIlkSiraNo"].(string); ok && esasIlk != "" {
				if esasSon, ok := filters["esasSonSiraNo"].(string); ok && esasSon != "" {
					// Convert string to int for comparison
					esasIlkNum, _ := strconv.Atoi(esasIlk)
					esasSonNum, _ := strconv.Atoi(esasSon)
					paramCount++
					countParamCount++
					// Use LIKE for year filtering and check range
					query += fmt.Sprintf(" AND esas_no LIKE $%d", paramCount)
					countQuery += fmt.Sprintf(" AND esas_no LIKE $%d", countParamCount)
					args = append(args, fmt.Sprintf("%s/%%", esasYil))
					countArgs = append(countArgs, fmt.Sprintf("%s/%%", esasYil))
					
					// Add number range check if both limits provided
					if esasIlkNum > 0 && esasSonNum > 0 {
						paramCount += 2
						countParamCount += 2
						query += fmt.Sprintf(" AND CAST(SUBSTRING(esas_no FROM '\\d+/([0-9]+)') AS INTEGER) BETWEEN $%d AND $%d", paramCount-1, paramCount)
						countQuery += fmt.Sprintf(" AND CAST(SUBSTRING(esas_no FROM '\\d+/([0-9]+)') AS INTEGER) BETWEEN $%d AND $%d", countParamCount-1, countParamCount)
						args = append(args, esasIlkNum, esasSonNum)
						countArgs = append(countArgs, esasIlkNum, esasSonNum)
					}
				}
			}
		}
		
		// Handle karar_no filters
		if kararYil, ok := filters["kararYil"].(string); ok && kararYil != "" {
			if kararIlk, ok := filters["kararIlkSiraNo"].(string); ok && kararIlk != "" {
				if kararSon, ok := filters["kararSonSiraNo"].(string); ok && kararSon != "" {
					// Convert string to int for comparison
					kararIlkNum, _ := strconv.Atoi(kararIlk)
					kararSonNum, _ := strconv.Atoi(kararSon)
					paramCount++
					countParamCount++
					// Use LIKE for year filtering
					query += fmt.Sprintf(" AND karar_no LIKE $%d", paramCount)
					countQuery += fmt.Sprintf(" AND karar_no LIKE $%d", countParamCount)
					args = append(args, fmt.Sprintf("%s/%%", kararYil))
					countArgs = append(countArgs, fmt.Sprintf("%s/%%", kararYil))
					
					// Add number range check if both limits provided
					if kararIlkNum > 0 && kararSonNum > 0 {
						paramCount += 2
						countParamCount += 2
						query += fmt.Sprintf(" AND CAST(SUBSTRING(karar_no FROM '\\d+/([0-9]+)') AS INTEGER) BETWEEN $%d AND $%d", paramCount-1, paramCount)
						countQuery += fmt.Sprintf(" AND CAST(SUBSTRING(karar_no FROM '\\d+/([0-9]+)') AS INTEGER) BETWEEN $%d AND $%d", countParamCount-1, countParamCount)
						args = append(args, kararIlkNum, kararSonNum)
						countArgs = append(countArgs, kararIlkNum, kararSonNum)
					}
				}
			}
		}
		
		// Handle date range filters
		if baslangic, ok := filters["baslangicTarihi"].(string); ok && baslangic != "" {
			paramCount++
			countParamCount++
			query += fmt.Sprintf(" AND karar_tarihi >= $%d", paramCount)
			countQuery += fmt.Sprintf(" AND karar_tarihi >= $%d", countParamCount)
			args = append(args, baslangic)
			countArgs = append(countArgs, baslangic)
		}
		
		if bitis, ok := filters["bitisTarihi"].(string); ok && bitis != "" {
			paramCount++
			countParamCount++
			query += fmt.Sprintf(" AND karar_tarihi <= $%d", paramCount)
			countQuery += fmt.Sprintf(" AND karar_tarihi <= $%d", countParamCount)
			args = append(args, bitis)
			countArgs = append(countArgs, bitis)
		}
	}
	
	// Get total count
	err := d.db.QueryRow(countQuery, countArgs...).Scan(&total)
	if err != nil {
		return nil, 0, err
	}
	
	// Handle sorting
	sortBy := "id"
	sortOrder := "DESC"
	if filters != nil {
		if sb, ok := filters["sortBy"].(string); ok && sb != "" {
			switch sb {
			case "karar_tarihi":
				sortBy = "karar_tarihi"
			case "esas_no":
				sortBy = "esas_no"
			case "karar_no":
				sortBy = "karar_no"
			}
		}
		if so, ok := filters["sortOrder"].(string); ok && so == "asc" {
			sortOrder = "ASC"
		}
	}
	
	// Add pagination
	query += fmt.Sprintf(" ORDER BY %s %s LIMIT $%d OFFSET $%d", sortBy, sortOrder, paramCount+1, paramCount+2)
	args = append(args, limit, offset)
	
	// Execute query
	rows, err := d.db.Query(query, args...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()
	
	for rows.Next() {
		var decisionID, daire, esasNo, kararNo, kararTarihi, decisionText sql.NullString
		err := rows.Scan(&decisionID, &daire, &esasNo, &kararNo, &kararTarihi, &decisionText)
		if err != nil {
			continue
		}
		
		decision := map[string]interface{}{
			"decision_id":   decisionID.String,
			"daire":        daire.String,
			"esas_no":      esasNo.String,
			"karar_no":     kararNo.String,
			"karar_tarihi": kararTarihi.String,
			"decision_text": decisionText.String,
		}
		decisions = append(decisions, decision)
	}
	
	return decisions, total, nil
}

func (d *Database) GetDecisionByID(decisionID string) (map[string]interface{}, error) {
	query := `SELECT decision_id, daire, esas_no, karar_no, karar_tarihi, mahkeme_adi, ilam_tarihi, decision_text, raw_html, created_at 
	          FROM decisions WHERE decision_id = $1`
	
	var id, daire, esasNo, kararNo, kararTarihi, mahkemeAdi, ilamTarihi, decisionText, rawHTML sql.NullString
	var createdAt time.Time
	
	err := d.db.QueryRow(query, decisionID).Scan(&id, &daire, &esasNo, &kararNo, &kararTarihi, &mahkemeAdi, &ilamTarihi, &decisionText, &rawHTML, &createdAt)
	if err != nil {
		return nil, err
	}
	
	return map[string]interface{}{
		"decision_id":   id.String,
		"daire":        daire.String,
		"esas_no":      esasNo.String,
		"karar_no":     kararNo.String,
		"karar_tarihi": kararTarihi.String,
		"mahkeme_adi":  mahkemeAdi.String,
		"ilam_tarihi":  ilamTarihi.String,
		"decision_text": decisionText.String,
		"raw_html":     rawHTML.String,
		"created_at":   createdAt.Format("2006-01-02 15:04:05"),
	}, nil
}

func (d *Database) GetDaires() ([]map[string]interface{}, error) {
	query := `SELECT daire, COUNT(*) as count 
	          FROM decisions 
	          WHERE daire IS NOT NULL AND daire != ''
	          GROUP BY daire 
	          ORDER BY count DESC`
	
	rows, err := d.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	
	var daires []map[string]interface{}
	for rows.Next() {
		var daire string
		var count int
		if err := rows.Scan(&daire, &count); err != nil {
			continue
		}
		daires = append(daires, map[string]interface{}{
			"name":  daire,
			"count": count,
		})
	}
	
	return daires, nil
}

func (d *Database) SearchDecisions(query string, page, limit int) ([]map[string]interface{}, int, error) {
	return d.GetDecisions(page, limit, query, "")
}

func (d *Database) Close() error {
	if d.db != nil {
		return d.db.Close()
	}
	return nil
}