services:
  # Main Hukukat Application (Development)
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: hukukat-app
    ports:
      - "8020:8020"
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=hukukat
      - POSTGRES_PASSWORD=hukukat_secret_2024
      - POSTGRES_DB=hukukat_db
      - EMBEDDING_SERVICE_URL=http://embedding-service:8021
      - AUTO_SCRAPE=${AUTO_SCRAPE:-true}
      - SCRAPE_START=${SCRAPE_START:-1}
      - SCRAPE_END=${SCRAPE_END:-100000}
      - SCRAPE_SIZE=${SCRAPE_SIZE:-20}
    volumes:
      - .:/app
    depends_on:
      postgres:
        condition: service_healthy
      embedding-service:
        condition: service_healthy
    networks:
      - hukukat-network
    command: ["air", "-c", ".air.toml"]
    restart: unless-stopped

  # PostgreSQL with pgvector for semantic search
  postgres:
    image: pgvector/pgvector:pg16
    container_name: hukukat-postgres
    environment:
      POSTGRES_USER: hukukat
      POSTGRES_PASSWORD: hukukat_secret_2024
      POSTGRES_DB: hukukat_db
      POSTGRES_HOST_AUTH_METHOD: md5
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - hukukat-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hukukat -d hukukat_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # BGE-M3 Embedding Service
  embedding-service:
    build:
      context: .
      dockerfile: Dockerfile.embedding
    container_name: hukukat-embeddings
    ports:
      - "8021:8021"
    environment:
      - PORT=8021
    networks:
      - hukukat-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8021/health"]
      interval: 30s
      timeout: 10s
      start_period: 60s
      retries: 3
    restart: unless-stopped

volumes:
  postgres-data:
    name: hukukat-postgres-data

networks:
  hukukat-network:
    name: hukukat-network
