# Hukukat Deployment Guide

## Architecture Overview

3 services required:

1. **PostgreSQL with pgvector** - Vector database for semantic search
2. **Embedding Service** - BGE-M3 model for text embeddings  
3. **Main Application** - Web UI + API + Crawler

## Services

### 1. PostgreSQL Database
**Docker Image:** `pgvector/pgvector:pg16`

**Purpose:** 
- Stores court decisions
- Vector embeddings for semantic search
- Full-text search support

**Required ENV:**
```bash
POSTGRES_USER=hukukat
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=hukukat_db
```

**Run:**
```bash
docker run -d --name postgres \
  -e POSTGRES_USER=hukukat \
  -e POSTGRES_PASSWORD=your_secure_password \
  -e POSTGRES_DB=hukukat_db \
  -p 5432:5432 \
  pgvector/pgvector:pg16
```

### 2. Embedding Service
**Dockerfile:** `Dockerfile.embedding`

**Purpose:**
- Converts text to vectors using BGE-M3 model
- Provides embedding API for semantic search
- Runs on port 8021

**Build & Run:**
```bash
docker build -f Dockerfile.embedding -t embedding .
docker run -d --name embedding -p 8021:8021 embedding
```

### 3. Main Application
**Dockerfile:** `Dockerfile`

**Purpose:**
- Web interface (port 8020)
- REST API for decisions
- Crawler for 9.3M court decisions
- Semantic & full-text search

**Required ENV:**
```bash
POSTGRES_HOST=postgres_ip
POSTGRES_PORT=5432
POSTGRES_USER=hukukat
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=hukukat_db
EMBEDDING_SERVICE_URL=http://embedding_ip:8021
```

**Optional ENV (Crawler Settings):**
```bash
# No need to set these - defaults are configured for full crawl
AUTO_SCRAPE=true          # Enable crawler (default: true)
SCRAPE_START=1            # Starting page (default: 1)
SCRAPE_END=933388         # End page for 9.3M decisions
SCRAPE_SIZE=10            # Decisions per page
```

**Build & Run:**
```bash
docker build -f Dockerfile -t hukukat .
docker run -d --name hukukat \
  -e POSTGRES_HOST=postgres_ip \
  -e POSTGRES_PASSWORD=your_secure_password \
  -e EMBEDDING_SERVICE_URL=http://embedding_ip:8021 \
  -p 8020:8020 \
  hukukat
```

## Network Setup

All services must be able to communicate:
- Main App → PostgreSQL (5432)
- Main App → Embedding Service (8021)
- External → Main App (8020)

## Resource Requirements

- **PostgreSQL:** 2GB RAM, 50GB+ disk
- **Embedding:** 2GB RAM (model loading)
- **Main App:** 1GB RAM
- **Total:** 5GB RAM minimum, 8GB recommended

## Startup Order

1. Start PostgreSQL first
2. Start Embedding Service (takes 30-60s to load model)
3. Start Main Application

## Health Check

```bash
# PostgreSQL
curl postgres_ip:5432

# Embedding Service
curl http://embedding_ip:8021/health

# Main Application
curl http://main_app_ip:8020/health
```

## Notes

- Initial crawl takes 7-10 days for all 9.3M decisions
- Crawler has adaptive rate limiting to prevent blocking
- Automatically resumes from last position after restart
- Database grows to ~30-50GB with all decisions