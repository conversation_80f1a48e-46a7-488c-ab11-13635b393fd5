package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// AdaptiveScraper uses adaptive rate limiting and worker management
type AdaptiveScraper struct {
	db                *Database
	parser            *Parser
	currentWorkers    int
	maxWorkers        int
	requestCount      int64
	successStreak     int64
	lastRequestTime   time.Time
	minDelay          time.Duration
	currentDelay      time.Duration
	warmupRequests    int
	isWarmedUp        bool
	mu                sync.RWMutex
	stats             *ScraperStats
	embeddingManager  *EmbeddingManager
	decisionCache     map[string]bool  // Memory cache for existing decisions
	cacheMu           sync.RWMutex     // Separate mutex for cache
}

// NewAdaptiveScraper creates a scraper with adaptive rate limiting
func NewAdaptiveScraper(db *Database) *AdaptiveScraper {
	return &AdaptiveScraper{
		db:               db,
		parser:           NewParser(),
		currentWorkers:   1,     // Start with single worker
		maxWorkers:       5,      // Max workers when warmed up
		minDelay:         1 * time.Second,  // Faster minimum delay
		currentDelay:     2 * time.Second,  // Start a bit faster
		warmupRequests:   30,     // Reduced warm-up period
		stats:            &ScraperStats{StartTime: time.Now()},
		decisionCache:    make(map[string]bool),
		// embeddingManager will be set by main
	}
}

// ProcessWithAdaptiveRate processes pages with adaptive rate control
func (s *AdaptiveScraper) ProcessWithAdaptiveRate(ctx context.Context, startPage, endPage, pageSize int) error {
	log.Printf("🎯 Adaptive scraper başlatılıyor (warm-up: %d requests)", s.warmupRequests)
	
	for page := startPage; page <= endPage; page++ {
		if err := s.processPageAdaptive(ctx, page, pageSize); err != nil {
			log.Printf("❌ Sayfa %d işlenirken hata: %v", page, err)
			
			// On error, reduce workers and increase delay
			s.mu.Lock()
			if s.currentWorkers > 1 {
				s.currentWorkers--
				log.Printf("📉 Worker sayısı düşürüldü: %d", s.currentWorkers)
			}
			s.currentDelay = time.Duration(float64(s.currentDelay) * 1.5)
			if s.currentDelay > 30*time.Second {
				s.currentDelay = 30 * time.Second
			}
			s.mu.Unlock()
		}
		
		// Wait between pages
		if page < endPage {
			s.mu.RLock()
			delay := s.currentDelay
			s.mu.RUnlock()
			log.Printf("⏸️ Sayfalar arası bekleme: %v", delay)
			time.Sleep(delay)
		}
	}
	
	return nil
}

func (s *AdaptiveScraper) processPageAdaptive(ctx context.Context, pageNumber, pageSize int) error {
	s.mu.RLock()
	workers := s.currentWorkers
	s.mu.RUnlock()
	
	log.Printf("📄 Sayfa %d işleniyor (%d worker, delay: %v)", pageNumber, workers, s.currentDelay)
	
	// Fetch items with metadata
	fetcher := NewFetcher()
	items, err := fetcher.FetchDecisionIDs(pageNumber, pageSize)
	if err != nil {
		return fmt.Errorf("sayfa alınamadı: %w", err)
	}
	
	if len(items) == 0 {
		log.Printf("⚠️ Sayfa %d'de karar bulunamadı", pageNumber)
		return nil
	}
	
	// First check cache
	s.cacheMu.RLock()
	cachedCount := 0
	uncachedItems := []APISearchItem{}
	for _, item := range items {
		if s.decisionCache[item.ID] {
			cachedCount++
		} else {
			uncachedItems = append(uncachedItems, item)
		}
	}
	s.cacheMu.RUnlock()
	
	if cachedCount > 0 {
		log.Printf("💾 Cache hit: %d kayıt cache'den atlandı", cachedCount)
	}
	
	// Batch check uncached decisions from database
	existsMap := make(map[string]bool)
	if len(uncachedItems) > 0 {
		var err error
		uncachedIDs := make([]string, len(uncachedItems))
		for i, item := range uncachedItems {
			uncachedIDs[i] = item.ID
		}
		existsMap, err = s.db.CheckMultipleDecisions(uncachedIDs)
		if err != nil {
			log.Printf("⚠️ Batch kontrol hatası: %v", err)
			existsMap = make(map[string]bool)
		}
		
		// Update cache with database results
		s.cacheMu.Lock()
		for id, exists := range existsMap {
			if exists {
				s.decisionCache[id] = true
			}
		}
		s.cacheMu.Unlock()
	}
	
	// Filter out existing decisions and prepare work items with metadata
	workQueue := make(chan WorkItem, len(items))
	newCount := 0
	skipCount := cachedCount
	for _, item := range uncachedItems {
		if exists, found := existsMap[item.ID]; found && exists {
			skipCount++
			continue
		}
		workQueue <- WorkItem{
			ID:          item.ID,
			PageNumber:  pageNumber,
			Daire:       item.Daire,
			EsasNo:      item.EsasNo,
			KararNo:     item.KararNo,
			KararTarihi: item.KararTarihi,
			Index:       item.Index,
		}
		newCount++
	}
	close(workQueue)
	
	if skipCount > 0 {
		log.Printf("⏭️ Sayfa %d: %d mevcut kayıt atlandı, %d yeni kayıt işlenecek", pageNumber, skipCount, newCount)
	}
	
	var wg sync.WaitGroup
	results := make(chan bool, len(items))
	
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go s.adaptiveWorker(ctx, i+1, workQueue, results, &wg)
	}
	
	go func() {
		wg.Wait()
		close(results)
	}()
	
	// Collect results and adapt
	successCount := 0
	errorCount := 0
	for success := range results {
		if success {
			successCount++
			atomic.AddInt64(&s.successStreak, 1)
		} else {
			errorCount++
			atomic.StoreInt64(&s.successStreak, 0) // Reset streak on error
		}
	}
	
	// Adapt based on results
	s.adaptParameters(successCount, errorCount, len(items))
	
	log.Printf("📊 Sayfa %d: %d başarılı, %d hata (Workers: %d, Delay: %v)", 
		pageNumber, successCount, errorCount, s.currentWorkers, s.currentDelay)
	
	return nil
}

func (s *AdaptiveScraper) adaptiveWorker(ctx context.Context, id int, queue <-chan WorkItem, results chan<- bool, wg *sync.WaitGroup) {
	defer wg.Done()
	
	fetcher := NewFetcher()
	
	for item := range queue {
		select {
		case <-ctx.Done():
			return
		default:
		}
		
		// Apply rate limiting
		s.applyAdaptiveRateLimit()
		
		// Process decision
		success := s.processDecisionWithTracking(fetcher, item, id)
		results <- success
	}
}

func (s *AdaptiveScraper) processDecisionWithTracking(fetcher *Fetcher, item WorkItem, workerID int) bool {
	requestNum := atomic.AddInt64(&s.requestCount, 1)
	
	// During warmup, be extra careful
	if requestNum <= int64(s.warmupRequests) {
		log.Printf("🔥 [W%d] Warm-up #%d/%d: ID %s", workerID, requestNum, s.warmupRequests, item.ID)
		time.Sleep(2 * time.Second) // Extra delay during warmup
	}
	
	// Fetch with single retry
	html, err := fetcher.FetchDecisionHTML(item.ID)
	if err != nil {
		log.Printf("❌ [W%d] #%d ID %s hata: %v", workerID, requestNum, item.ID, err)
		
		// Check if CAPTCHA error
		if strings.Contains(err.Error(), "CAPTCHA") || strings.Contains(err.Error(), "rate limited") {
			log.Printf("🛑 CAPTCHA/Rate limit tespit edildi! 60 saniye bekleniyor...")
			time.Sleep(60 * time.Second)
			
			// Increase delay significantly
			s.mu.Lock()
			s.currentDelay = 10 * time.Second
			s.currentWorkers = 1
			s.mu.Unlock()
		}
		
		// If we get errors after many successes, we might be rate limited
		if requestNum > 50 {
			log.Printf("⚠️ Rate limit olasılığı! %d başarılı istekten sonra hata", requestNum-1)
		}
		return false
	}
	
	// Parse and save
	decision := s.parser.ParseHTML(html, item.ID)
	
	// Use metadata from API - API data takes priority
	if item.Daire != "" {
		decision.Daire = item.Daire
	}
	if item.EsasNo != "" {
		decision.EsasNo = item.EsasNo
	}
	if item.KararNo != "" {
		decision.KararNo = item.KararNo
	}
	if item.KararTarihi != "" {
		decision.KararTarihi = item.KararTarihi
	}
	
	if err := s.db.SaveDecision(decision); err != nil {
		log.Printf("❌ [W%d] ID %s kaydedilemedi: %v", workerID, item.ID, err)
		return false
	}
	
	// Add to cache
	s.cacheMu.Lock()
	s.decisionCache[item.ID] = true
	s.cacheMu.Unlock()
	
	atomic.AddInt64(&s.stats.TotalSuccess, 1)
	log.Printf("✅ [W%d] #%d ID %s kaydedildi", workerID, requestNum, item.ID)
	
	// Queue decision for embedding generation if embedding manager exists
	if s.embeddingManager != nil {
		s.embeddingManager.QueueDecision(*decision)
	}
	
	return true
}

func (s *AdaptiveScraper) applyAdaptiveRateLimit() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	// Calculate time since last request
	if !s.lastRequestTime.IsZero() {
		elapsed := time.Since(s.lastRequestTime)
		if elapsed < s.currentDelay {
			time.Sleep(s.currentDelay - elapsed)
		}
	}
	
	s.lastRequestTime = time.Now()
}

func (s *AdaptiveScraper) adaptParameters(successCount, errorCount, total int) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	errorRate := float64(errorCount) / float64(total)
	successRate := float64(successCount) / float64(total)
	
	// Check if warmed up
	if atomic.LoadInt64(&s.requestCount) >= int64(s.warmupRequests) && !s.isWarmedUp {
		s.isWarmedUp = true
		log.Printf("🔥 Warm-up tamamlandı! Hız artırılıyor...")
	}
	
	// Adapt based on error rate
	if errorRate > 0.5 {
		// High error rate - back off significantly
		s.currentWorkers = 1
		s.currentDelay = time.Duration(float64(s.currentDelay) * 2)
		if s.currentDelay > 30*time.Second {
			s.currentDelay = 30 * time.Second
		}
		log.Printf("🛑 Yüksek hata oranı (%.0f%%) - Tek worker, delay: %v", errorRate*100, s.currentDelay)
		
	} else if errorRate > 0.2 {
		// Moderate error rate - reduce workers
		if s.currentWorkers > 1 {
			s.currentWorkers--
		}
		s.currentDelay = time.Duration(float64(s.currentDelay) * 1.3)
		log.Printf("⚠️ Orta hata oranı (%.0f%%) - Workers: %d, delay: %v", errorRate*100, s.currentWorkers, s.currentDelay)
		
	} else if successRate > 0.9 && s.isWarmedUp {
		// High success rate and warmed up - can increase
		streak := atomic.LoadInt64(&s.successStreak)
		if streak > 20 {
			if s.currentWorkers < s.maxWorkers {
				s.currentWorkers++
				log.Printf("📈 Başarı serisi (%d) - Workers artırıldı: %d", streak, s.currentWorkers)
			}
			if s.currentDelay > s.minDelay {
				s.currentDelay = time.Duration(float64(s.currentDelay) * 0.8)
				if s.currentDelay < s.minDelay {
					s.currentDelay = s.minDelay
				}
			}
			atomic.StoreInt64(&s.successStreak, 0) // Reset streak
		}
	}
}

// GetAdaptiveStats returns current adaptive scraper statistics
func (s *AdaptiveScraper) GetAdaptiveStats() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	return map[string]interface{}{
		"total_requests":   atomic.LoadInt64(&s.requestCount),
		"total_success":    atomic.LoadInt64(&s.stats.TotalSuccess),
		"current_workers":  s.currentWorkers,
		"current_delay":    s.currentDelay.String(),
		"is_warmed_up":     s.isWarmedUp,
		"success_streak":   atomic.LoadInt64(&s.successStreak),
		"elapsed_time":     time.Since(s.stats.StartTime).String(),
	}
}