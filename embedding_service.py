#!/usr/bin/env python3
"""
Hukukat Embedding Service - BGE-M3
Turkish legal document semantic embedding service
"""

import os
import json
import time
import logging
import warnings
from typing import List, Dict, Any
from flask import Flask, request, jsonify
import numpy as np

# Suppress the specific tokenizer warning from transformers
warnings.filterwarnings("ignore", message=".*fast tokenizer.*__call__.*")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Global model instance
model = None

def load_model():
    """Load BGE-M3 model with optimizations"""
    global model
    try:
        from FlagEmbedding import BGEM3FlagModel
        
        logger.info("Loading BGE-M3 model...")
        start_time = time.time()
        
        model = BGEM3FlagModel(
            'BAAI/bge-m3',
            use_fp16=True,  # Memory optimization
            device='cpu'    # CPU for Docker compatibility
        )
        
        load_time = time.time() - start_time
        logger.info(f"✅ BGE-M3 model loaded in {load_time:.2f}s")
        
        # Warm-up inference with batch_size to avoid warning
        logger.info("Warming up model...")
        test_text = "Bu bir test cümlesidir."
        _ = model.encode(
            [test_text],
            batch_size=1,
            return_dense=True,
            return_sparse=False,
            convert_to_numpy=True
        )
        logger.info("✅ Model warmed up")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to load model: {e}")
        return False

def preprocess_legal_text(text: str) -> str:
    """Preprocess Turkish legal text for better embedding"""
    if not text:
        return ""
    
    # Clean HTML remnants
    import re
    text = re.sub(r'<[^>]+>', ' ', text)
    text = re.sub(r'&\w+;', ' ', text)
    
    # Normalize whitespace
    text = ' '.join(text.split())
    
    # Truncate if too long (BGE-M3 limit: 8192 tokens)
    if len(text) > 6000:  # Conservative limit
        text = text[:6000] + "..."
    
    return text.strip()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None,
        'timestamp': time.time()
    })

@app.route('/embed', methods=['POST'])
def generate_embeddings():
    """Generate embeddings for input texts"""
    try:
        data = request.get_json()
        
        if not data or 'texts' not in data:
            return jsonify({'error': 'Missing texts field'}), 400
        
        texts = data['texts']
        if not isinstance(texts, list):
            texts = [texts]
        
        if len(texts) == 0:
            return jsonify({'error': 'Empty texts list'}), 400
        
        if len(texts) > 100:  # Batch size limit
            return jsonify({'error': 'Too many texts (max 100)'}), 400
        
        # Preprocess texts
        processed_texts = [preprocess_legal_text(text) for text in texts]
        
        # Filter empty texts
        valid_texts = [t for t in processed_texts if t]
        if not valid_texts:
            return jsonify({'error': 'No valid texts after preprocessing'}), 400
        
        logger.info(f"Generating embeddings for {len(valid_texts)} texts")
        start_time = time.time()
        
        # Generate dense embeddings (for similarity search)
        # Use batch_size to avoid tokenizer warning
        embeddings = model.encode(
            valid_texts, 
            batch_size=32,
            return_dense=True, 
            return_sparse=False,
            convert_to_numpy=True
        )
        
        # BGE-M3 returns a dict with 'dense_vecs' key when return_dense=True
        if isinstance(embeddings, dict) and 'dense_vecs' in embeddings:
            embeddings = embeddings['dense_vecs']
        
        # Convert to list for JSON serialization
        if isinstance(embeddings, np.ndarray):
            embeddings = embeddings.tolist()
        
        processing_time = time.time() - start_time
        
        logger.info(f"✅ Generated embeddings in {processing_time:.3f}s "
                   f"({processing_time/len(valid_texts):.3f}s per text)")
        
        return jsonify({
            'embeddings': embeddings,
            'count': len(embeddings),
            'dimensions': len(embeddings[0]) if embeddings else 0,
            'processing_time': processing_time,
            'model': 'BGE-M3'
        })
        
    except Exception as e:
        logger.error(f"❌ Embedding generation failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/embed-batch', methods=['POST'])
def batch_embeddings():
    """Batch embedding endpoint for large datasets"""
    logger.info("Batch endpoint called")
    try:
        logger.info("Getting JSON data")
        data = request.get_json()
        logger.info(f"Got data: {type(data)}")
        
        decisions = data.get('decisions', [])
        logger.info(f"Got {len(decisions)} decisions")
        if not decisions:
            return jsonify({'error': 'No decisions provided'}), 400
        
        results = []
        batch_size = 50  # Process in smaller batches
        
        logger.info(f"Processing {len(decisions)} decisions in batches of {batch_size}")
        
        for i in range(0, len(decisions), batch_size):
            logger.info(f"Processing batch starting at index {i}")
            batch = decisions[i:i + batch_size]
            logger.info(f"Batch size: {len(batch)}")
            batch_texts = []
            batch_ids = []
            
            for decision in batch:
                decision_id = decision.get('decision_id', '')
                text = decision.get('decision_text', '')
                
                if decision_id and text:
                    processed_text = preprocess_legal_text(text)
                    batch_texts.append(processed_text)
                    batch_ids.append(decision_id)
                    logger.info(f"Added decision {decision_id} with text length {len(processed_text)}")
            
            if batch_texts:
                logger.info(f"Encoding {len(batch_texts)} texts")
                # Generate embeddings with batch_size to avoid tokenizer warning
                embeddings = model.encode(
                    batch_texts, 
                    batch_size=32,
                    return_dense=True, 
                    return_sparse=False,
                    convert_to_numpy=True
                )
                logger.info(f"Encoding complete, type: {type(embeddings)}")
                
                # BGE-M3 returns a dict with 'dense_vecs' key
                if isinstance(embeddings, dict) and 'dense_vecs' in embeddings:
                    embeddings = embeddings['dense_vecs']
                
                # embeddings should now be a numpy array
                if isinstance(embeddings, np.ndarray):
                    logger.info(f"Embeddings shape: {embeddings.shape}, batch_ids count: {len(batch_ids)}")
                    
                    # Handle both 1D and 2D arrays
                    if embeddings.ndim == 1:
                        # Single embedding returned as 1D array
                        if len(batch_ids) == 1:
                            results.append({
                                'decision_id': batch_ids[0],
                                'embedding': embeddings.tolist()
                            })
                    elif embeddings.ndim == 2:
                        # Multiple embeddings as 2D array
                        for j, decision_id in enumerate(batch_ids):
                            if j < len(embeddings):
                                results.append({
                                    'decision_id': decision_id,
                                    'embedding': embeddings[j].tolist()
                                })
                    else:
                        logger.error(f"Unexpected embeddings dimensions: {embeddings.ndim}")
                else:
                    logger.error(f"Unexpected embeddings type: {type(embeddings)}")
            
            if batch_size > 0:
                logger.info(f"Processed batch {i//batch_size + 1}/{(len(decisions) + batch_size - 1)//batch_size}")
        
        return jsonify({
            'results': results,
            'total_processed': len(results),
            'model': 'BGE-M3'
        })
        
    except Exception as e:
        logger.error(f"❌ Batch processing failed: {e}")
        import traceback
        try:
            logger.error(f"Traceback:\n{traceback.format_exc()}")
        except:
            logger.error("Could not get traceback")
        return jsonify({'error': str(e)}), 500

@app.route('/similarity', methods=['POST'])
def compute_similarity():
    """Compute similarity between query and texts"""
    try:
        data = request.get_json()
        
        query = data.get('query', '')
        texts = data.get('texts', [])
        
        if not query:
            return jsonify({'error': 'Missing query'}), 400
        
        if not texts:
            return jsonify({'error': 'Missing texts'}), 400
        
        # Preprocess
        query = preprocess_legal_text(query)
        texts = [preprocess_legal_text(t) for t in texts]
        
        # Generate embeddings with batch_size to avoid tokenizer warning
        all_texts = [query] + texts
        embeddings = model.encode(
            all_texts, 
            batch_size=32,
            return_dense=True, 
            return_sparse=False,
            convert_to_numpy=True
        )
        
        # Compute similarities
        query_embedding = embeddings[0]
        text_embeddings = embeddings[1:]
        
        similarities = []
        for text_emb in text_embeddings:
            # Cosine similarity
            similarity = np.dot(query_embedding, text_emb) / (
                np.linalg.norm(query_embedding) * np.linalg.norm(text_emb)
            )
            similarities.append(float(similarity))
        
        return jsonify({
            'similarities': similarities,
            'query': query[:100] + '...' if len(query) > 100 else query,
            'count': len(similarities)
        })
        
    except Exception as e:
        logger.error(f"❌ Similarity computation failed: {e}")
        return jsonify({'error': str(e)}), 500

# Load model on startup (for both development and production)
logger.info("🚀 Starting Hukukat Embedding Service")
if not load_model():
    logger.error("Failed to load model, exiting")
    exit(1)

if __name__ == '__main__':
    # Only for development - production uses Gunicorn
    port = int(os.environ.get('PORT', 8021))
    logger.info(f"Starting development server on port {port}...")
    app.run(host='0.0.0.0', port=port, debug=False)