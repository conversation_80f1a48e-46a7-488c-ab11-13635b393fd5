package main

import (
	"context"
	"fmt"
	"log"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"
)

// OptimizedScraper is a high-performance concurrent scraper
type OptimizedScraper struct {
	db               *Database
	parser           *Parser
	workers          int
	rateLimit        time.Duration
	userAgents       []string
	stats            *ScraperStats
	semaphore        chan struct{}
	stop<PERSON>han         chan bool
	batchSize        int
	batchDelay       time.Duration
	consecutiveErrs  int
	mu               sync.Mutex
	embeddingManager *EmbeddingManager
}

// ScraperStats tracks scraping statistics
type ScraperStats struct {
	TotalProcessed     int64
	TotalSuccess       int64
	TotalSkipped       int64
	TotalErrors        int64
	StartTime          time.Time
	LastRateLimitAt    time.Time
	ConsecutiveSuccess int64
	LastSuccessAt      time.Time
}

// WorkItem represents a decision to process
type WorkItem struct {
	ID          string
	PageNumber  int
	RetryCount  int
	// Metadata from API
	Daire       string
	EsasNo      string
	KararNo     string
	KararTarihi string
	Index       int
}

// NewOptimizedScraper creates an optimized scraper with worker pool
func NewOptimizedScraper(db *Database, workers int) *OptimizedScraper {
	if workers <= 0 {
		workers = 5 // Default workers
	}

	return &OptimizedScraper{
		db:         db,
		parser:     NewParser(),
		workers:    workers,
		rateLimit:  500 * time.Millisecond, // Start conservative
		batchSize:  30,                      // Process in smaller batches
		batchDelay: 5 * time.Second,         // Pause between batches
		userAgents: []string{
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
			"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		},
		stats:     &ScraperStats{StartTime: time.Now()},
		semaphore: make(chan struct{}, workers),
		stopChan:  make(chan bool),
	}
}

// ProcessPageConcurrent processes a page with concurrent workers
func (s *OptimizedScraper) ProcessPageConcurrent(ctx context.Context, pageNumber, pageSize int) error {
	log.Printf("🚀 Sayfa %d işleniyor (%d worker ile)...", pageNumber, s.workers)

	// Create fetcher with random user agent
	fetcher := s.createFetcherWithRotation()

	// Fetch decision items with metadata
	items, err := fetcher.FetchDecisionIDs(pageNumber, pageSize)
	if err != nil {
		return fmt.Errorf("sayfa %d alınamadı: %w", pageNumber, err)
	}

	if len(items) == 0 {
		log.Printf("⚠️ Sayfa %d'de karar bulunamadı", pageNumber)
		return nil
	}

	// Process in batches to avoid rate limiting
	for i := 0; i < len(items); i += s.batchSize {
		end := i + s.batchSize
		if end > len(items) {
			end = len(items)
		}
		batch := items[i:end]
		
		log.Printf("📦 Batch %d/%d işleniyor (%d karar)...", i/s.batchSize+1, (len(items)+s.batchSize-1)/s.batchSize, len(batch))
		
		// Extract IDs for batch check
		batchIDs := make([]string, len(batch))
		for j, item := range batch {
			batchIDs[j] = item.ID
		}
		
		// Batch check existing decisions
		existsMap, err := s.db.CheckMultipleDecisions(batchIDs)
		if err != nil {
			log.Printf("⚠️ Batch kontrol hatası: %v", err)
			existsMap = make(map[string]bool)
		}
		
		// Create work queue for this batch (only new items)
		workQueue := make(chan WorkItem, len(batch))
		newCount := 0
		skipCount := 0
		for _, item := range batch {
			if exists, found := existsMap[item.ID]; found && exists {
				skipCount++
				atomic.AddInt64(&s.stats.TotalSkipped, 1)
				continue
			}
			workQueue <- WorkItem{
				ID:          item.ID,
				PageNumber:  pageNumber,
				Daire:       item.Daire,
				EsasNo:      item.EsasNo,
				KararNo:     item.KararNo,
				KararTarihi: item.KararTarihi,
				Index:       item.Index,
			}
			newCount++
		}
		close(workQueue)
		
		if skipCount > 0 {
			log.Printf("⏭️ Batch: %d mevcut kayıt atlandı, %d yeni kayıt işlenecek", skipCount, newCount)
		}

	// Start workers
	var wg sync.WaitGroup
	results := make(chan bool, len(batch))

	for i := 0; i < s.workers; i++ {
		wg.Add(1)
		go s.worker(ctx, i+1, workQueue, results, &wg)
	}

		// Wait for completion
		go func() {
			wg.Wait()
			close(results)
		}()

		// Collect results
		batchSuccess := 0
		batchErrors := 0
		for success := range results {
			if success {
				batchSuccess++
			} else {
				batchErrors++
			}
		}
		
		log.Printf("📊 Batch tamamlandı: %d başarılı, %d hata", batchSuccess, batchErrors)
		
		// Check if we need to back off
		if batchErrors > len(batch)/2 {
			log.Printf("⚠️ Yüksek hata oranı! %d saniye bekleniyor...", 30)
			time.Sleep(30 * time.Second)
			s.rateLimit = time.Duration(float64(s.rateLimit) * 2)
		} else if i+s.batchSize < len(items) {
			// Wait between batches
			log.Printf("⏸️ Batch arası bekleme: %v", s.batchDelay)
			time.Sleep(s.batchDelay)
		}
	}

	// Update stats
	processed := atomic.LoadInt64(&s.stats.TotalProcessed)
	success := atomic.LoadInt64(&s.stats.TotalSuccess)
	skipped := atomic.LoadInt64(&s.stats.TotalSkipped)
	errors := atomic.LoadInt64(&s.stats.TotalErrors)

	log.Printf("📊 Sayfa %d tamamlandı | Toplam: %d işlendi, %d başarılı, %d atlandı, %d hata",
		pageNumber, processed, success, skipped, errors)

	return nil
}

// worker is a concurrent worker that processes decisions
func (s *OptimizedScraper) worker(ctx context.Context, id int, queue <-chan WorkItem, results chan<- bool, wg *sync.WaitGroup) {
	defer wg.Done()

	fetcher := s.createFetcherWithRotation()

	for item := range queue {
		select {
		case <-ctx.Done():
			return
		case <-s.stopChan:
			return
		default:
		}

		// Acquire semaphore
		s.semaphore <- struct{}{}
		success := s.processDecision(fetcher, item, id)
		<-s.semaphore

		results <- success

		// Dynamic rate limiting
		s.applyRateLimit()
	}
}

// processDecision processes a single decision
func (s *OptimizedScraper) processDecision(fetcher *Fetcher, item WorkItem, workerID int) bool {
	atomic.AddInt64(&s.stats.TotalProcessed, 1)
	
	// Check if we're being rate limited
	s.mu.Lock()
	if s.consecutiveErrs > 5 {
		log.Printf("🛑 [W%d] Circuit breaker active, waiting 60s...", workerID)
		s.mu.Unlock()
		time.Sleep(60 * time.Second)
		s.mu.Lock()
		s.consecutiveErrs = 0
	}
	s.mu.Unlock()

	// Fetch HTML with retry
	var html string
	var err error
	maxRetries := 3
	for retry := 0; retry < maxRetries; retry++ {
		html, err = fetcher.FetchDecisionHTML(item.ID)
		if err == nil {
			break
		}

		if retry < maxRetries-1 {
			waitTime := time.Duration(1<<retry) * time.Second
			log.Printf("⚠️ [W%d] ID %s hata (deneme %d/%d): %v. %v bekliyor...",
				workerID, item.ID, retry+1, maxRetries, err, waitTime)
			time.Sleep(waitTime)
		}
	}

	if err != nil {
		atomic.AddInt64(&s.stats.TotalErrors, 1)
		log.Printf("❌ [W%d] ID %s indirilemedi: %v", workerID, item.ID, err)
		
		s.mu.Lock()
		s.consecutiveErrs++
		s.mu.Unlock()
		
		return false
	}

	// Parse and save
	decision := s.parser.ParseHTML(html, item.ID)
	
	// Use metadata from API - API data takes priority
	if item.Daire != "" {
		decision.Daire = item.Daire
	}
	if item.EsasNo != "" {
		decision.EsasNo = item.EsasNo
	}
	if item.KararNo != "" {
		decision.KararNo = item.KararNo
	}
	if item.KararTarihi != "" {
		decision.KararTarihi = item.KararTarihi
	}
	
	if err := s.db.SaveDecision(decision); err != nil {
		atomic.AddInt64(&s.stats.TotalErrors, 1)
		log.Printf("❌ [W%d] ID %s kaydedilemedi: %v", workerID, item.ID, err)
		return false
	}

	atomic.AddInt64(&s.stats.TotalSuccess, 1)
	atomic.AddInt64(&s.stats.ConsecutiveSuccess, 1)
	s.stats.LastSuccessAt = time.Now()
	
	s.mu.Lock()
	s.consecutiveErrs = 0 // Reset error counter on success
	s.mu.Unlock()
	
	log.Printf("✅ [W%d] ID %s kaydedildi (Daire: %s, Tarih: %s)", workerID, item.ID, decision.Daire, decision.KararTarihi)
	
	// Queue decision for embedding generation if embedding manager exists
	if s.embeddingManager != nil {
		s.embeddingManager.QueueDecision(*decision)
	}
	return true
}

// createFetcherWithRotation creates a fetcher with random user agent
func (s *OptimizedScraper) createFetcherWithRotation() *Fetcher {
	fetcher := NewFetcher()
	// Override user agent
	if len(s.userAgents) > 0 {
		ua := s.userAgents[rand.Intn(len(s.userAgents))]
		fetcher.SetUserAgent(ua)
	}
	return fetcher
}

// applyRateLimit applies dynamic rate limiting
func (s *OptimizedScraper) applyRateLimit() {
	// Check recent error rate (last 20 requests)
	errors := atomic.LoadInt64(&s.stats.TotalErrors)
	processed := atomic.LoadInt64(&s.stats.TotalProcessed)
	consecutiveSuccess := atomic.LoadInt64(&s.stats.ConsecutiveSuccess)
	
	if processed > 0 {
		errorRate := float64(errors) / float64(processed)
		
		// More aggressive rate limiting
		if errorRate > 0.2 {
			// High error rate, slow down significantly
			s.rateLimit = time.Duration(float64(s.rateLimit) * 2)
			if s.rateLimit > 10*time.Second {
				s.rateLimit = 10 * time.Second
			}
			log.Printf("⚠️ Yüksek hata oranı (%.1f%%), hız düşürülüyor: %v", errorRate*100, s.rateLimit)
		} else if consecutiveSuccess > 20 && s.rateLimit > 500*time.Millisecond {
			// Only speed up after sustained success
			s.rateLimit = time.Duration(float64(s.rateLimit) * 0.9)
			if s.rateLimit < 500*time.Millisecond {
				s.rateLimit = 500 * time.Millisecond
			}
			atomic.StoreInt64(&s.stats.ConsecutiveSuccess, 0) // Reset counter
		}
	}

	// Apply rate limit with more jitter
	jitter := time.Duration(rand.Intn(500)) * time.Millisecond
	time.Sleep(s.rateLimit + jitter)
}

// GetStats returns current statistics
func (s *OptimizedScraper) GetStats() map[string]interface{} {
	elapsed := time.Since(s.stats.StartTime)
	processed := atomic.LoadInt64(&s.stats.TotalProcessed)
	
	rate := float64(0)
	if elapsed.Seconds() > 0 {
		rate = float64(processed) / elapsed.Seconds()
	}

	return map[string]interface{}{
		"total_processed": processed,
		"total_success":   atomic.LoadInt64(&s.stats.TotalSuccess),
		"total_skipped":   atomic.LoadInt64(&s.stats.TotalSkipped),
		"total_errors":    atomic.LoadInt64(&s.stats.TotalErrors),
		"elapsed_time":    elapsed.String(),
		"rate_per_second": fmt.Sprintf("%.2f", rate),
		"workers":         s.workers,
		"rate_limit":      s.rateLimit.String(),
	}
}