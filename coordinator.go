package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"sync/atomic"
	"time"
)

// Coordinator manages multiple workers and distributes work
type Coordinator struct {
	db           *OptimizedDatabase
	workers      map[string]*WorkerInfo
	workersMutex sync.RWMutex
	pageQueue    chan PageJob
	resultQueue  chan JobResult
	stats        *CoordinatorStats
	config       *CoordinatorConfig
	ctx          context.Context
	cancel       context.CancelFunc
}

// WorkerInfo tracks information about each worker
type WorkerInfo struct {
	ID             string    `json:"id"`
	Host           string    `json:"host"`
	Status         string    `json:"status"` // idle, working, error, offline
	LastSeen       time.Time `json:"last_seen"`
	CurrentJob     *PageJob  `json:"current_job,omitempty"`
	ProcessedCount int64     `json:"processed_count"`
	ErrorCount     int64     `json:"error_count"`
	AverageTime    float64   `json:"average_time_ms"`
}

// PageJob represents a scraping job
type PageJob struct {
	ID         int       `json:"id"`
	PageStart  int       `json:"page_start"`
	PageEnd    int       `json:"page_end"`
	PageSize   int       `json:"page_size"`
	Priority   int       `json:"priority"`
	RetryCount int       `json:"retry_count"`
	AssignedTo string    `json:"assigned_to"`
	AssignedAt time.Time `json:"assigned_at"`
	CreatedAt  time.Time `json:"created_at"`
}

// JobResult contains the result of a scraping job
type JobResult struct {
	JobID      int        `json:"job_id"`
	WorkerID   string     `json:"worker_id"`
	Success    bool       `json:"success"`
	Decisions  []Decision `json:"decisions,omitempty"`
	Error      string     `json:"error,omitempty"`
	StartTime  time.Time  `json:"start_time"`
	EndTime    time.Time  `json:"end_time"`
	RetryCount int        `json:"retry_count"`
}

// CoordinatorStats tracks overall statistics
type CoordinatorStats struct {
	TotalPages     int64     `json:"total_pages"`
	ProcessedPages int64     `json:"processed_pages"`
	FailedPages    int64     `json:"failed_pages"`
	TotalDecisions int64     `json:"total_decisions"`
	StartTime      time.Time `json:"start_time"`
	ActiveWorkers  int32     `json:"active_workers"`
	QueueSize      int       `json:"queue_size"`
	ProcessingRate float64   `json:"processing_rate"`
}

// CoordinatorConfig holds configuration
type CoordinatorConfig struct {
	MaxWorkers      int           `json:"max_workers"`
	PageBatchSize   int           `json:"page_batch_size"`
	QueueSize       int           `json:"queue_size"`
	WorkerTimeout   time.Duration `json:"worker_timeout"`
	JobTimeout      time.Duration `json:"job_timeout"`
	MaxRetries      int           `json:"max_retries"`
	CheckpointEvery int           `json:"checkpoint_every"`
}

// NewCoordinator creates a new coordinator
func NewCoordinator(db *OptimizedDatabase, config *CoordinatorConfig) *Coordinator {
	if config == nil {
		config = &CoordinatorConfig{
			MaxWorkers:      10,
			PageBatchSize:   100,
			QueueSize:       1000,
			WorkerTimeout:   30 * time.Second,
			JobTimeout:      5 * time.Minute,
			MaxRetries:      3,
			CheckpointEvery: 1000,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &Coordinator{
		db:          db,
		workers:     make(map[string]*WorkerInfo),
		pageQueue:   make(chan PageJob, config.QueueSize),
		resultQueue: make(chan JobResult, config.QueueSize),
		stats:       &CoordinatorStats{StartTime: time.Now()},
		config:      config,
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start begins the coordinator
func (c *Coordinator) Start() {
	log.Println("🎯 Coordinator starting...")

	// Start result processor
	go c.processResults()

	// Start worker health monitor
	go c.monitorWorkers()

	// Start statistics reporter
	go c.reportStats()

	// Start HTTP API
	go c.startAPI()

	log.Println("✅ Coordinator started")
}

// AddWorker registers a new worker
func (c *Coordinator) AddWorker(id, host string) error {
	c.workersMutex.Lock()
	defer c.workersMutex.Unlock()

	if _, exists := c.workers[id]; exists {
		return fmt.Errorf("worker %s already exists", id)
	}

	c.workers[id] = &WorkerInfo{
		ID:       id,
		Host:     host,
		Status:   "idle",
		LastSeen: time.Now(),
	}

	atomic.AddInt32(&c.stats.ActiveWorkers, 1)
	log.Printf("✅ Worker %s registered from %s", id, host)
	return nil
}

// RemoveWorker unregisters a worker
func (c *Coordinator) RemoveWorker(id string) {
	c.workersMutex.Lock()
	defer c.workersMutex.Unlock()

	if _, exists := c.workers[id]; exists {
		delete(c.workers, id)
		atomic.AddInt32(&c.stats.ActiveWorkers, -1)
		log.Printf("❌ Worker %s removed", id)
	}
}

// QueuePages adds page ranges to the queue
func (c *Coordinator) QueuePages(startPage, endPage, pageSize int) error {
	jobID := 1
	for page := startPage; page <= endPage; page += c.config.PageBatchSize {
		endBatch := page + c.config.PageBatchSize - 1
		if endBatch > endPage {
			endBatch = endPage
		}

		job := PageJob{
			ID:        jobID,
			PageStart: page,
			PageEnd:   endBatch,
			PageSize:  pageSize,
			Priority:  1,
			CreatedAt: time.Now(),
		}

		select {
		case c.pageQueue <- job:
			atomic.AddInt64(&c.stats.TotalPages, int64(endBatch-page+1))
			jobID++
		case <-time.After(5 * time.Second):
			return fmt.Errorf("queue full, couldn't add job")
		}
	}

	log.Printf("📦 Queued %d jobs for pages %d-%d", jobID-1, startPage, endPage)
	return nil
}

// GetNextJob returns the next job for a worker
func (c *Coordinator) GetNextJob(workerID string) (*PageJob, error) {
	c.workersMutex.Lock()
	worker, exists := c.workers[workerID]
	if !exists {
		c.workersMutex.Unlock()
		return nil, fmt.Errorf("worker %s not registered", workerID)
	}
	worker.Status = "waiting"
	worker.LastSeen = time.Now()
	c.workersMutex.Unlock()

	select {
	case job := <-c.pageQueue:
		c.workersMutex.Lock()
		job.AssignedTo = workerID
		job.AssignedAt = time.Now()
		worker.Status = "working"
		worker.CurrentJob = &job
		c.workersMutex.Unlock()

		log.Printf("📤 Assigned job %d (pages %d-%d) to worker %s",
			job.ID, job.PageStart, job.PageEnd, workerID)
		return &job, nil

	case <-time.After(10 * time.Second):
		return nil, nil // No jobs available
	}
}

// SubmitResult processes a job result from a worker
func (c *Coordinator) SubmitResult(result JobResult) error {
	c.workersMutex.Lock()
	worker, exists := c.workers[result.WorkerID]
	if exists {
		worker.Status = "idle"
		worker.CurrentJob = nil
		worker.LastSeen = time.Now()

		if result.Success {
			worker.ProcessedCount++
			processingTime := result.EndTime.Sub(result.StartTime).Milliseconds()
			worker.AverageTime = (worker.AverageTime*float64(worker.ProcessedCount-1) + float64(processingTime)) / float64(worker.ProcessedCount)
		} else {
			worker.ErrorCount++
		}
	}
	c.workersMutex.Unlock()

	select {
	case c.resultQueue <- result:
		return nil
	case <-time.After(5 * time.Second):
		return fmt.Errorf("result queue full")
	}
}

// processResults handles job results
func (c *Coordinator) processResults() {
	batchID := 1
	decisionCount := 0

	for {
		select {
		case result := <-c.resultQueue:
			if result.Success {
				// Batch insert decisions
				for _, decision := range result.Decisions {
					processingTime := result.EndTime.Sub(result.StartTime).Milliseconds() / int64(len(result.Decisions))
					decision.ProcessingTimeMs = processingTime

					if err := c.db.BatchInsert(decision, result.WorkerID, batchID); err != nil {
						log.Printf("Failed to insert decision: %v", err)
					}
					decisionCount++
				}

				atomic.AddInt64(&c.stats.ProcessedPages, 1)
				atomic.AddInt64(&c.stats.TotalDecisions, int64(len(result.Decisions)))

				log.Printf("✅ Job %d completed by %s: %d decisions",
					result.JobID, result.WorkerID, len(result.Decisions))

				// Checkpoint periodically
				if decisionCount >= c.config.CheckpointEvery {
					c.db.FlushBatch()
					c.db.Checkpoint()
					decisionCount = 0
					batchID++
				}
			} else {
				atomic.AddInt64(&c.stats.FailedPages, 1)
				log.Printf("❌ Job %d failed by %s: %s", result.JobID, result.WorkerID, result.Error)

				// Re-queue if under retry limit
				if result.RetryCount < c.config.MaxRetries {
					// TODO: Re-queue the job
				}
			}

		case <-c.ctx.Done():
			return
		}
	}
}

// monitorWorkers checks worker health
func (c *Coordinator) monitorWorkers() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.workersMutex.Lock()
			now := time.Now()
			for id, worker := range c.workers {
				if now.Sub(worker.LastSeen) > c.config.WorkerTimeout {
					if worker.Status != "offline" {
						worker.Status = "offline"
						log.Printf("⚠️ Worker %s went offline", id)

						// Re-queue current job if any
						if worker.CurrentJob != nil {
							worker.CurrentJob.RetryCount++
							c.pageQueue <- *worker.CurrentJob
						}
					}
				}
			}
			c.workersMutex.Unlock()

		case <-c.ctx.Done():
			return
		}
	}
}

// reportStats periodically reports statistics
func (c *Coordinator) reportStats() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.stats.QueueSize = len(c.pageQueue)

			elapsed := time.Since(c.stats.StartTime).Minutes()
			if elapsed > 0 {
				c.stats.ProcessingRate = float64(c.stats.ProcessedPages) / elapsed
			}

			progress, _ := c.db.GetProgress()

			log.Printf("📊 Stats: Pages: %d/%d | Decisions: %d | Workers: %d | Queue: %d | Rate: %.1f pages/min",
				c.stats.ProcessedPages,
				c.stats.TotalPages,
				c.stats.TotalDecisions,
				c.stats.ActiveWorkers,
				c.stats.QueueSize,
				c.stats.ProcessingRate,
			)

			log.Printf("📈 DB Stats: %v", progress)

		case <-c.ctx.Done():
			return
		}
	}
}

// startAPI starts the HTTP API server
func (c *Coordinator) startAPI() {
	mux := http.NewServeMux()

	// Register worker
	mux.HandleFunc("/api/coordinator/register", func(w http.ResponseWriter, r *http.Request) {
		var req struct {
			WorkerID string `json:"worker_id"`
			Host     string `json:"host"`
		}
		json.NewDecoder(r.Body).Decode(&req)

		err := c.AddWorker(req.WorkerID, req.Host)
		if err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{"status": "registered"})
	})

	// Get next job
	mux.HandleFunc("/api/coordinator/job", func(w http.ResponseWriter, r *http.Request) {
		workerID := r.URL.Query().Get("worker_id")
		job, err := c.GetNextJob(workerID)
		if err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		if job == nil {
			w.WriteHeader(http.StatusNoContent)
			return
		}

		json.NewEncoder(w).Encode(job)
	})

	// Submit result
	mux.HandleFunc("/api/coordinator/result", func(w http.ResponseWriter, r *http.Request) {
		var result JobResult
		if err := json.NewDecoder(r.Body).Decode(&result); err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}

		if err := c.SubmitResult(result); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
	})

	// Get stats
	mux.HandleFunc("/api/coordinator/stats", func(w http.ResponseWriter, r *http.Request) {
		stats := map[string]interface{}{
			"coordinator": c.stats,
			"workers":     c.workers,
			"queue_size":  len(c.pageQueue),
		}
		json.NewEncoder(w).Encode(stats)
	})

	log.Println("🌐 Coordinator API listening on :8021")
	http.ListenAndServe(":8021", mux)
}

// Stop gracefully stops the coordinator
func (c *Coordinator) Stop() {
	log.Println("🛑 Stopping coordinator...")
	c.cancel()

	// Flush any pending data
	c.db.FlushBatch()
	c.db.Close()

	log.Println("✅ Coordinator stopped")
}
