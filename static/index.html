<!DOCTYPE html>
<html lang="tr" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hukukat - Türkiye'nin Akıllı Hukuk Arama Motoru</title>
    <meta name="description" content="Yargıtay kararlarında yapay zeka destekli anlamsal arama. Hu<PERSON><PERSON> metinlerde hızlı ve akıllı arama.">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        slate: {
                            850: '#0f1729'
                        }
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --primary: #1e3a8a;
            --primary-dark: #1e293b;
            --gold: #fbbf24;
        }
        
        /* Dark mode variables */
        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --border: #334155;
        }
        
        [data-theme="light"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --border: #e2e8f0;
        }
        
        /* Smooth transitions */
        * {
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .loader { border-top-color: var(--primary); }
        .animate-spin { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
        .highlight { background-color: #fef3c7; }
        
        /* Brand gradient */
        .brand-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        /* Mobile optimizations */
        @media (max-width: 640px) {
            .mobile-menu {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .mobile-menu.active {
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="h-full bg-gray-50 dark:bg-slate-900 transition-colors">
    <!-- Header -->
    <header class="bg-white dark:bg-slate-800 shadow-sm border-b dark:border-slate-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <!-- Logo and Brand -->
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-900 to-purple-800 rounded-lg flex items-center justify-center">
                            <i class="fas fa-balance-scale text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold brand-gradient">Hukukat</h1>
                            <p class="text-xs text-gray-500 dark:text-gray-400 hidden sm:block">Akıllı Hukuk Arama Motoru</p>
                        </div>
                    </div>
                </div>
                
                <!-- Desktop Stats and Controls -->
                <div class="hidden md:flex items-center space-x-6">
                    <div id="stats" class="flex space-x-6 text-sm">
                        <div class="text-gray-600 dark:text-gray-300">
                            <i class="fas fa-database mr-1"></i>
                            <span id="totalCount">-</span> Karar
                        </div>
                        <div class="text-gray-600 dark:text-gray-300">
                            <i class="fas fa-calendar mr-1"></i>
                            Bugün: <span id="todayCount">-</span>
                        </div>
                        <div class="text-gray-600 dark:text-gray-300" title="Anlamsal arama için işlenmiş kararlar">
                            <i class="fas fa-brain mr-1"></i>
                            İşlenmiş: <span id="embeddingCount">-</span>/<span id="embeddingTotal">-</span>
                            <span id="embeddingPercentage" class="text-xs text-green-600 font-semibold ml-1"></span>
                        </div>
                    </div>
                    
                    <!-- Dark Mode Toggle -->
                    <button onclick="toggleTheme()" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700 transition">
                        <i id="themeIcon" class="fas fa-moon text-gray-600 dark:text-gray-300"></i>
                    </button>
                </div>
                
                <!-- Mobile Menu Button -->
                <button onclick="toggleMobileMenu()" class="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-700">
                    <i class="fas fa-bars text-gray-600 dark:text-gray-300"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Stats Drawer -->
    <div id="mobileMenu" class="md:hidden fixed inset-x-0 top-16 bg-white dark:bg-slate-800 shadow-lg z-40 mobile-menu p-4">
        <div class="space-y-3 text-sm">
            <div class="flex justify-between text-gray-600 dark:text-gray-300">
                <span><i class="fas fa-database mr-2"></i>Toplam</span>
                <span class="font-semibold" id="totalCountMobile">-</span>
            </div>
            <div class="flex justify-between text-gray-600 dark:text-gray-300">
                <span><i class="fas fa-calendar mr-2"></i>Bugün</span>
                <span class="font-semibold" id="todayCountMobile">-</span>
            </div>
            <div class="flex justify-between text-gray-600 dark:text-gray-300">
                <span><i class="fas fa-brain mr-2"></i>İşlenmiş</span>
                <span class="font-semibold">
                    <span id="embeddingCountMobile">-</span>/<span id="embeddingTotalMobile">-</span>
                </span>
            </div>
            <hr class="dark:border-slate-600">
            <button onclick="toggleTheme()" class="w-full flex justify-between items-center p-2 rounded hover:bg-gray-100 dark:hover:bg-slate-700">
                <span class="text-gray-600 dark:text-gray-300">
                    <i class="fas fa-moon mr-2"></i>Karanlık Mod
                </span>
                <i class="fas fa-toggle-on text-2xl text-gray-400" id="themeToggleMobile"></i>
            </button>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Search and Filter Bar -->
        <div class="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-4 mb-6">
            <!-- Search Type Toggle -->
            <div class="mb-4 flex justify-center">
                <div class="bg-gray-100 dark:bg-slate-700 rounded-lg p-1 flex">
                    <button 
                        id="textSearchBtn"
                        onclick="setSearchType('text')"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors bg-white dark:bg-slate-600 text-gray-900 dark:text-gray-100 shadow-sm"
                    >
                        <i class="fas fa-search mr-2"></i>Metin Araması
                    </button>
                    <button 
                        id="semanticSearchBtn"
                        onclick="setSearchType('semantic')"
                        class="px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-700 dark:text-gray-300"
                    >
                        <i class="fas fa-brain mr-2"></i>Anlamsal Arama
                    </button>
                </div>
            </div>

            <!-- Main Search Bar -->
            <div class="flex flex-col lg:flex-row gap-4 mb-4">
                <div class="flex-1">
                    <div class="relative">
                        <input 
                            type="text" 
                            id="searchInput" 
                            placeholder="Karar metni içinde ara..."
                            class="w-full pl-10 pr-4 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                        >
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400 dark:text-gray-500"></i>
                    </div>
                </div>
                <button 
                    onclick="searchDecisions()" 
                    class="px-6 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition"
                >
                    <i id="searchIcon" class="fas fa-search mr-2"></i><span id="searchText">Ara</span>
                </button>
                <button 
                    onclick="toggleAdvancedFilters()" 
                    id="advancedFiltersBtn"
                    class="px-6 py-2 bg-gray-200 dark:bg-slate-600 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-slate-500 transition"
                >
                    <i class="fas fa-filter mr-2"></i>Gelişmiş Filtreler
                </button>
                <button 
                    onclick="resetFilters()" 
                    class="px-6 py-2 bg-gray-200 dark:bg-slate-600 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-slate-500 transition"
                >
                    <i class="fas fa-redo mr-2"></i>Sıfırla
                </button>
            </div>

            <!-- Advanced Filters Panel (Hidden by default) -->
            <div id="advancedFilters" class="hidden border-t dark:border-slate-700 pt-4 mt-4 space-y-4">
                <!-- Notice for Semantic Search -->
                <div id="semanticSearchNotice" class="hidden p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded">
                    <div class="flex items-center text-sm text-blue-700 dark:text-blue-400">
                        <i class="fas fa-info-circle mr-2"></i>
                        <span>Gelişmiş filtreler anlamsal aramada kullanılamaz. Metin aramasına geçiniz.</span>
                    </div>
                </div>

                <!-- Daireler Section -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Kurullar -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-university mr-1"></i>Kurullar
                        </label>
                        <div class="border dark:border-slate-600 rounded-lg p-3 bg-gray-50 dark:bg-slate-700 max-h-40 overflow-y-auto space-y-2">
                            <label class="flex items-center text-sm">
                                <input type="checkbox" value="Hukuk Genel Kurulu" class="kurul-checkbox mr-2">
                                <span class="text-gray-700 dark:text-gray-300">Hukuk Genel Kurulu</span>
                            </label>
                            <label class="flex items-center text-sm">
                                <input type="checkbox" value="Ceza Genel Kurulu" class="kurul-checkbox mr-2">
                                <span class="text-gray-700 dark:text-gray-300">Ceza Genel Kurulu</span>
                            </label>
                            <label class="flex items-center text-sm">
                                <input type="checkbox" value="Büyük Genel Kurulu" class="kurul-checkbox mr-2">
                                <span class="text-gray-700 dark:text-gray-300">Büyük Genel Kurulu</span>
                            </label>
                            <label class="flex items-center text-sm">
                                <input type="checkbox" value="Ceza Daireleri Başkanlar Kurulu" class="kurul-checkbox mr-2">
                                <span class="text-gray-700 dark:text-gray-300">Ceza Daireleri Başkanlar K.</span>
                            </label>
                            <label class="flex items-center text-sm">
                                <input type="checkbox" value="Hukuk Daireleri Başkanlar Kurulu" class="kurul-checkbox mr-2">
                                <span class="text-gray-700 dark:text-gray-300">Hukuk Daireleri Başkanlar K.</span>
                            </label>
                        </div>
                    </div>

                    <!-- Hukuk Daireleri -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-balance-scale mr-1"></i>Hukuk Daireleri
                        </label>
                        <div class="border dark:border-slate-600 rounded-lg p-3 bg-gray-50 dark:bg-slate-700 max-h-40 overflow-y-auto">
                            <div class="flex justify-between mb-2">
                                <button onclick="selectAllHukuk()" class="text-xs text-blue-600 dark:text-blue-400 hover:underline">Tümünü Seç</button>
                                <button onclick="deselectAllHukuk()" class="text-xs text-gray-600 dark:text-gray-400 hover:underline">Temizle</button>
                            </div>
                            <div id="hukukDaireleri" class="space-y-1 text-sm"></div>
                        </div>
                    </div>

                    <!-- Ceza Daireleri -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-gavel mr-1"></i>Ceza Daireleri
                        </label>
                        <div class="border dark:border-slate-600 rounded-lg p-3 bg-gray-50 dark:bg-slate-700 max-h-40 overflow-y-auto">
                            <div class="flex justify-between mb-2">
                                <button onclick="selectAllCeza()" class="text-xs text-blue-600 dark:text-blue-400 hover:underline">Tümünü Seç</button>
                                <button onclick="deselectAllCeza()" class="text-xs text-gray-600 dark:text-gray-400 hover:underline">Temizle</button>
                            </div>
                            <div id="cezaDaireleri" class="space-y-1 text-sm"></div>
                        </div>
                    </div>
                </div>

                <!-- Numara ve Tarih Filtreleri -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Esas No -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-folder mr-1"></i>Esas Numarası
                        </label>
                        <div class="flex gap-2">
                            <input type="number" id="esasYil" placeholder="Yıl" min="2000" max="2030" 
                                class="w-24 px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <input type="number" id="esasIlk" placeholder="İlk Sıra" min="1" 
                                class="flex-1 px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <span class="self-center text-gray-500">-</span>
                            <input type="number" id="esasSon" placeholder="Son Sıra" min="1" 
                                class="flex-1 px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        </div>
                    </div>

                    <!-- Karar No -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-file-alt mr-1"></i>Karar Numarası
                        </label>
                        <div class="flex gap-2">
                            <input type="number" id="kararYil" placeholder="Yıl" min="2000" max="2030" 
                                class="w-24 px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <input type="number" id="kararIlk" placeholder="İlk Sıra" min="1" 
                                class="flex-1 px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <span class="self-center text-gray-500">-</span>
                            <input type="number" id="kararSon" placeholder="Son Sıra" min="1" 
                                class="flex-1 px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        </div>
                    </div>
                </div>

                <!-- Tarih Aralığı -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-calendar mr-1"></i>Karar Tarihi
                    </label>
                    <div class="flex gap-4">
                        <div class="flex-1">
                            <input type="date" id="tarihBaslangic" 
                                class="w-full px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <span class="text-xs text-gray-500 dark:text-gray-400">Başlangıç</span>
                        </div>
                        <div class="flex-1">
                            <input type="date" id="tarihBitis" 
                                class="w-full px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <span class="text-xs text-gray-500 dark:text-gray-400">Bitiş</span>
                        </div>
                    </div>
                </div>

                <!-- Sıralama -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-sort mr-1"></i>Sıralama Kriteri
                        </label>
                        <select id="siralamaKriteri" class="w-full px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">Karar Tarihine Göre</option>
                            <option value="2">Esas No'ya Göre</option>
                            <option value="3">Karar No'ya Göre</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            <i class="fas fa-sort-amount-down mr-1"></i>Sıralama Yönü
                        </label>
                        <select id="siralamaYonu" class="w-full px-3 py-2 border dark:border-slate-600 bg-white dark:bg-slate-700 text-gray-900 dark:text-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="desc">Büyükten Küçüğe</option>
                            <option value="asc">Küçükten Büyüğe</option>
                        </select>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mt-6 flex justify-end gap-2">
                    <button 
                        onclick="clearAllFilters()"
                        class="px-4 py-2 bg-gray-300 dark:bg-slate-600 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-400 dark:hover:bg-slate-500 transition"
                    >
                        <i class="fas fa-times mr-2"></i>Temizle
                    </button>
                    <button 
                        onclick="applyFilters()"
                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
                    >
                        <i class="fas fa-check mr-2"></i>Filtreleri Uygula
                    </button>
                </div>
            </div>

            <!-- Embedding Service Status -->
            <div id="embeddingStatus" class="hidden mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
                <div class="flex items-center text-sm text-yellow-700 dark:text-yellow-400">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span>Anlamsal arama servisi hazırlanıyor... Lütfen bekleyin.</span>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results" class="space-y-4">
            <!-- Loader -->
            <div id="loader" class="hidden">
                <div class="flex justify-center py-12">
                    <div class="loader animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-gray-100"></div>
                </div>
            </div>

            <!-- Decision List -->
            <div id="decisionList" class="space-y-4"></div>

            <!-- Pagination -->
            <div id="pagination" class="flex justify-center mt-8 space-x-2"></div>
        </div>
    </main>

    <!-- Decision Detail Modal -->
    <div id="detailModal" class="hidden fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border dark:border-slate-700 w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-slate-800">
            <div class="flex justify-between items-start mb-4">
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-100" id="modalTitle">Karar Detayı</h3>
                <button onclick="closeModal()" class="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-2xl"></i>
                </button>
            </div>
            <div id="modalContent" class="mt-2 max-h-96 overflow-y-auto text-gray-700 dark:text-gray-300"></div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        let currentSearch = '';
        let currentDaire = '';
        let searchType = 'text'; // 'text' or 'semantic'
        let embeddingServiceAvailable = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize theme
            initTheme();
            
            loadStats();
            loadDecisions();
            initializeDaireler();
            checkEmbeddingService();

            // Enter key search
            document.getElementById('searchInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') searchDecisions();
            });
        });
        
        // Theme Management
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            document.documentElement.classList.toggle('dark', savedTheme === 'dark');
            document.body.classList.toggle('dark', savedTheme === 'dark');
            updateThemeIcon(savedTheme);
        }
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            document.documentElement.classList.toggle('dark', newTheme === 'dark');
            document.body.classList.toggle('dark', newTheme === 'dark');
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        }
        
        function updateThemeIcon(theme) {
            const icon = document.getElementById('themeIcon');
            const mobileToggle = document.getElementById('themeToggleMobile');
            
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun text-yellow-400' : 'fas fa-moon text-gray-600 dark:text-gray-300';
            }
            if (mobileToggle) {
                mobileToggle.className = theme === 'dark' ? 'fas fa-toggle-on text-2xl text-blue-500' : 'fas fa-toggle-off text-2xl text-gray-400';
            }
        }
        
        // Mobile Menu
        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('active');
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                
                // Update both desktop and mobile stats
                ['totalCount', 'totalCountMobile'].forEach(id => {
                    const el = document.getElementById(id);
                    if (el) el.textContent = stats.total_decisions || 0;
                });
                
                ['todayCount', 'todayCountMobile'].forEach(id => {
                    const el = document.getElementById(id);
                    if (el) el.textContent = stats.today_decisions || 0;
                });
                
                // Update embedding statistics
                if (stats.embeddings) {
                    document.getElementById('embeddingCount').textContent = stats.embeddings.processed || 0;
                    document.getElementById('embeddingTotal').textContent = stats.embeddings.total || 0;
                    const percentage = stats.embeddings.percentage || '0';
                    document.getElementById('embeddingPercentage').textContent = `(${percentage}%)`;
                    
                    // Color coding based on percentage
                    const percentageEl = document.getElementById('embeddingPercentage');
                    const pct = parseFloat(percentage);
                    if (pct === 100) {
                        percentageEl.className = 'text-xs text-green-600 font-semibold ml-1';
                    } else if (pct >= 75) {
                        percentageEl.className = 'text-xs text-blue-600 font-semibold ml-1';
                    } else if (pct >= 50) {
                        percentageEl.className = 'text-xs text-yellow-600 font-semibold ml-1';
                    } else {
                        percentageEl.className = 'text-xs text-orange-600 font-semibold ml-1';
                    }
                }
            } catch (error) {
                console.error('Stats yüklenemedi:', error);
            }
        }

        async function loadDaires() {
            try {
                const response = await fetch('/api/daires');
                const daires = await response.json();
                const select = document.getElementById('daireFilter');
                daires.forEach(daire => {
                    const option = document.createElement('option');
                    option.value = daire.name;
                    option.textContent = `${daire.name} (${daire.count})`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Daireler yüklenemedi:', error);
            }
        }

        async function loadDecisions(page = 1) {
            currentPage = page;
            const loader = document.getElementById('loader');
            const list = document.getElementById('decisionList');
            
            // Remove any existing semantic search info
            const existingSearchInfo = document.getElementById('semanticSearchInfo');
            if (existingSearchInfo) {
                existingSearchInfo.remove();
            }
            
            loader.classList.remove('hidden');
            list.innerHTML = '';

            try {
                // Get advanced filters
                const filters = getAdvancedFilters();
                
                let url = `/api/decisions?page=${page}&limit=10`;
                if (currentSearch) url += `&search=${encodeURIComponent(currentSearch)}`;
                
                // Add advanced filter parameters if available
                if (filters) {
                    // Add array parameters properly
                    filters.kurullar.forEach(k => url += `&kurullar[]=${encodeURIComponent(k)}`);
                    filters.hukukDaireler.forEach(h => url += `&hukukDaireler[]=${encodeURIComponent(h)}`);
                    filters.cezaDaireler.forEach(c => url += `&cezaDaireler[]=${encodeURIComponent(c)}`);
                    
                    // Add other filter fields
                    if (filters.esasYil) url += `&esasYil=${encodeURIComponent(filters.esasYil)}`;
                    if (filters.esasIlkSiraNo) url += `&esasIlkSiraNo=${encodeURIComponent(filters.esasIlkSiraNo)}`;
                    if (filters.esasSonSiraNo) url += `&esasSonSiraNo=${encodeURIComponent(filters.esasSonSiraNo)}`;
                    if (filters.kararYil) url += `&kararYil=${encodeURIComponent(filters.kararYil)}`;
                    if (filters.kararIlkSiraNo) url += `&kararIlkSiraNo=${encodeURIComponent(filters.kararIlkSiraNo)}`;
                    if (filters.kararSonSiraNo) url += `&kararSonSiraNo=${encodeURIComponent(filters.kararSonSiraNo)}`;
                    if (filters.baslangicTarihi) url += `&baslangicTarihi=${encodeURIComponent(filters.baslangicTarihi)}`;
                    if (filters.bitisTarihi) url += `&bitisTarihi=${encodeURIComponent(filters.bitisTarihi)}`;
                    if (filters.sortBy) url += `&sortBy=${encodeURIComponent(filters.sortBy)}`;
                    if (filters.sortOrder) url += `&sortOrder=${encodeURIComponent(filters.sortOrder)}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                renderDecisions(data.decisions || []);
                renderPagination(data.total_pages || 1);
            } catch (error) {
                console.error('Kararlar yüklenemedi:', error);
                list.innerHTML = '<div class="text-center text-red-600 py-8">Kararlar yüklenirken hata oluştu</div>';
            } finally {
                loader.classList.add('hidden');
            }
        }

        function renderDecisions(decisions) {
            const list = document.getElementById('decisionList');
            
            if (decisions.length === 0) {
                list.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-8">Karar bulunamadı</div>';
                return;
            }

            decisions.forEach(decision => {
                const card = document.createElement('div');
                card.className = 'bg-white dark:bg-slate-800 rounded-lg shadow-sm dark:shadow-slate-700/50 p-4 hover:shadow-md dark:hover:shadow-slate-700 transition cursor-pointer border border-transparent dark:border-slate-700';
                card.onclick = () => showDetail(decision.decision_id);

                const preview = decision.decision_text ? decision.decision_text.substring(0, 200) + '...' : '';
                
                card.innerHTML = `
                    <div class="flex flex-col sm:flex-row sm:justify-between">
                        <div class="flex-1">
                            <div class="flex flex-wrap items-center gap-2 sm:gap-4 mb-2">
                                <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">${decision.daire || 'Daire Belirtilmemiş'}</span>
                                <span class="text-xs sm:text-sm text-gray-500 dark:text-gray-400">ID: ${decision.decision_id}</span>
                            </div>
                            <div class="grid grid-cols-2 gap-2 mb-3 text-sm text-gray-700 dark:text-gray-300">
                                <div><i class="fas fa-folder mr-1 text-gray-400 dark:text-gray-500"></i> Esas: ${decision.esas_no || '-'}</div>
                                <div><i class="fas fa-gavel mr-1 text-gray-400 dark:text-gray-500"></i> Karar: ${decision.karar_no || '-'}</div>
                            </div>
                            <p class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">${highlightSearch(preview)}</p>
                        </div>
                        <button class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 ml-4">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                `;
                list.appendChild(card);
            });
        }

        function renderPagination(total) {
            totalPages = total;
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (total <= 1) return;

            // Previous button
            if (currentPage > 1) {
                pagination.innerHTML += `
                    <button onclick="loadDecisions(${currentPage - 1})" class="px-3 py-1 rounded border dark:border-slate-600 hover:bg-gray-100 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                `;
            }

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(total, currentPage + 2); i++) {
                const active = i === currentPage ? 
                    'bg-blue-600 dark:bg-blue-500 text-white border-blue-600 dark:border-blue-500' : 
                    'hover:bg-gray-100 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300 border dark:border-slate-600';
                pagination.innerHTML += `
                    <button onclick="loadDecisions(${i})" class="px-3 py-1 rounded ${active}">
                        ${i}
                    </button>
                `;
            }

            // Next button
            if (currentPage < total) {
                pagination.innerHTML += `
                    <button onclick="loadDecisions(${currentPage + 1})" class="px-3 py-1 rounded border dark:border-slate-600 hover:bg-gray-100 dark:hover:bg-slate-700 text-gray-700 dark:text-gray-300">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                `;
            }
        }

        async function searchDecisions() {
            currentSearch = document.getElementById('searchInput').value.trim();
            
            // Always reload with search - don't check daire filter here
            if (searchType === 'semantic' && currentSearch) {
                await performSemanticSearch();
            } else {
                loadDecisions(1);
            }
        }

        async function performSemanticSearch() {
            const loader = document.getElementById('loader');
            const list = document.getElementById('decisionList');
            const pagination = document.getElementById('pagination');
            
            loader.classList.remove('hidden');
            list.innerHTML = '';
            pagination.innerHTML = '';
            
            // Remove any existing search info
            const existingSearchInfo = document.getElementById('semanticSearchInfo');
            if (existingSearchInfo) {
                existingSearchInfo.remove();
            }

            try {
                const response = await fetch(`/api/semantic-search?q=${encodeURIComponent(currentSearch)}&limit=20`);
                const data = await response.json();

                if (response.ok) {
                    renderSemanticResults(data.results || []);
                    // Show search info
                    const searchInfo = document.createElement('div');
                    searchInfo.id = 'semanticSearchInfo';
                    searchInfo.className = 'mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg';
                    searchInfo.innerHTML = `
                        <div class="flex items-center text-sm text-blue-700 dark:text-blue-300">
                            <i class="fas fa-brain mr-2"></i>
                            <span>Anlamsal arama: <strong>${currentSearch}</strong> - ${data.count} sonuç bulundu</span>
                        </div>
                    `;
                    list.parentElement.insertBefore(searchInfo, list);
                } else {
                    throw new Error(data.error || 'Anlamsal arama başarısız');
                }
            } catch (error) {
                console.error('Anlamsal arama hatası:', error);
                list.innerHTML = `
                    <div class="text-center text-red-600 py-8">
                        <i class="fas fa-exclamation-triangle mb-2 text-2xl"></i>
                        <p>Anlamsal arama sırasında hata oluştu</p>
                        <p class="text-sm text-gray-500">${error.message}</p>
                    </div>
                `;
            } finally {
                loader.classList.add('hidden');
            }
        }

        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('daireFilter').value = '';
            currentSearch = '';
            currentDaire = '';
            loadDecisions(1);
        }

        function highlightSearch(text) {
            if (!currentSearch || !text) return text;
            const regex = new RegExp(`(${currentSearch})`, 'gi');
            return text.replace(regex, '<span class="highlight">$1</span>');
        }

        async function showDetail(decisionId) {
            const modal = document.getElementById('detailModal');
            const content = document.getElementById('modalContent');
            
            modal.classList.remove('hidden');
            content.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-2xl"></i></div>';

            try {
                const response = await fetch(`/api/decisions/${decisionId}`);
                const decision = await response.json();

                document.getElementById('modalTitle').textContent = `Karar: ${decision.decision_id}`;
                
                // Show decision details and HTML content directly
                content.innerHTML = `
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4 text-sm bg-gray-50 dark:bg-slate-700 p-3 rounded">
                            <div><strong>Daire:</strong> ${decision.daire || '-'}</div>
                            <div><strong>Tarih:</strong> ${decision.karar_tarihi || '-'}</div>
                            <div><strong>Esas No:</strong> ${decision.esas_no || '-'}</div>
                            <div><strong>Karar No:</strong> ${decision.karar_no || '-'}</div>
                        </div>
                        
                        <!-- Decision content - HTML directly from API -->
                        <div class="border-t dark:border-slate-600 pt-4">
                            <div class="prose prose-sm max-w-none dark:prose-invert">
                                ${decision.raw_html || '<div class="text-gray-500">Karar içeriği yüklenemedi</div>'}
                            </div>
                        </div>
                    </div>
                `;
            } catch (error) {
                content.innerHTML = '<div class="text-red-600 text-center">Karar detayı yüklenemedi</div>';
            }
        }

        function renderSemanticResults(results) {
            const list = document.getElementById('decisionList');
            
            if (results.length === 0) {
                list.innerHTML = '<div class="text-center text-gray-500 py-8">Anlamsal arama sonucu bulunamadı</div>';
                return;
            }

            results.forEach(result => {
                const card = document.createElement('div');
                card.className = 'bg-white dark:bg-slate-800 rounded-lg shadow-sm p-4 hover:shadow-md transition cursor-pointer border-l-4 border-blue-500';
                card.onclick = () => showDetail(result.decision_id);

                const preview = result.decision_text ? result.decision_text.substring(0, 200) + '...' : '';
                const similarity = Math.round(result.similarity * 100);
                
                card.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-4">
                                    <span class="text-sm font-semibold text-blue-600 dark:text-blue-400">${result.daire || 'Daire Belirtilmemiş'}</span>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">ID: ${result.decision_id}</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-2 py-1 rounded-full">
                                        ${similarity}% benzerlik
                                    </span>
                                    <i class="fas fa-brain text-blue-500 dark:text-blue-400" title="Anlamsal arama sonucu"></i>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-2 mb-3 text-sm">
                                <div class="text-gray-600 dark:text-gray-400"><i class="fas fa-folder mr-1 text-gray-400 dark:text-gray-500"></i> Esas: ${result.esas_no || '-'}</div>
                                <div class="text-gray-600 dark:text-gray-400"><i class="fas fa-gavel mr-1 text-gray-400 dark:text-gray-500"></i> Karar: ${result.karar_no || '-'}</div>
                            </div>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">${preview}</p>
                        </div>
                        <button class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 ml-4">
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                `;
                list.appendChild(card);
            });
        }

        function setSearchType(type) {
            searchType = type;
            const textBtn = document.getElementById('textSearchBtn');
            const semanticBtn = document.getElementById('semanticSearchBtn');
            const searchInput = document.getElementById('searchInput');
            const searchIcon = document.getElementById('searchIcon');
            const searchText = document.getElementById('searchText');

            if (type === 'semantic') {
                textBtn.className = 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-700 dark:text-gray-300';
                semanticBtn.className = 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-white dark:bg-slate-600 text-gray-900 dark:text-gray-100 shadow-sm';
                searchInput.placeholder = 'Anlamsal arama yapın... (örn: "kira sözleşmesi feshi" veya "tazminat davası")';
                searchIcon.className = 'fas fa-brain mr-2';
                searchText.textContent = 'Anlamsal Ara';
                
                // Disable advanced filters for semantic search
                const advancedFilters = document.getElementById('advancedFilters');
                if (advancedFilters) {
                    advancedFilters.classList.add('opacity-50', 'pointer-events-none');
                }
                const semanticWarning = document.getElementById('semanticSearchWarning');
                if (semanticWarning) {
                    semanticWarning.classList.remove('hidden');
                }
                
                if (!embeddingServiceAvailable) {
                    document.getElementById('embeddingStatus').classList.remove('hidden');
                }
            } else {
                textBtn.className = 'px-4 py-2 rounded-md text-sm font-medium transition-colors bg-white dark:bg-slate-600 text-gray-900 dark:text-gray-100 shadow-sm';
                semanticBtn.className = 'px-4 py-2 rounded-md text-sm font-medium transition-colors text-gray-700 dark:text-gray-300';
                searchInput.placeholder = 'Karar metni içinde ara...';
                searchIcon.className = 'fas fa-search mr-2';
                searchText.textContent = 'Ara';
                document.getElementById('embeddingStatus').classList.add('hidden');
                
                // Enable advanced filters for normal search
                const advancedFilters = document.getElementById('advancedFilters');
                if (advancedFilters) {
                    advancedFilters.classList.remove('opacity-50', 'pointer-events-none');
                }
                const semanticWarning = document.getElementById('semanticSearchWarning');
                if (semanticWarning) {
                    semanticWarning.classList.add('hidden');
                }
            }
        }

        async function checkEmbeddingService() {
            try {
                const response = await fetch('/api/embedding/health');
                const data = await response.json();
                
                if (response.ok && data.status === 'healthy') {
                    embeddingServiceAvailable = true;
                    document.getElementById('embeddingStatus').classList.add('hidden');
                } else {
                    embeddingServiceAvailable = false;
                    if (searchType === 'semantic') {
                        document.getElementById('embeddingStatus').classList.remove('hidden');
                    }
                }
            } catch (error) {
                embeddingServiceAvailable = false;
                console.log('Embedding service not available:', error);
            }
        }

        function closeModal() {
            document.getElementById('detailModal').classList.add('hidden');
        }

        // Advanced Filters Functions
        function toggleAdvancedFilters() {
            const panel = document.getElementById('advancedFilters');
            const btn = document.getElementById('advancedFiltersBtn');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                btn.innerHTML = '<i class="fas fa-filter mr-2"></i>Filtreleri Gizle';
            } else {
                panel.classList.add('hidden');
                btn.innerHTML = '<i class="fas fa-filter mr-2"></i>Gelişmiş Filtreler';
            }
            
            // Show notice for semantic search
            if (searchType === 'semantic') {
                document.getElementById('semanticSearchNotice').classList.remove('hidden');
            }
        }

        function initializeDaireler() {
            // Initialize Hukuk Daireleri
            const hukukContainer = document.getElementById('hukukDaireleri');
            for (let i = 1; i <= 23; i++) {
                hukukContainer.innerHTML += `
                    <label class="flex items-center">
                        <input type="checkbox" value="${i}. Hukuk Dairesi" class="hukuk-checkbox mr-2">
                        <span class="text-gray-700 dark:text-gray-300">${i}. Hukuk</span>
                    </label>
                `;
            }
            
            // Initialize Ceza Daireleri
            const cezaContainer = document.getElementById('cezaDaireleri');
            for (let i = 1; i <= 23; i++) {
                cezaContainer.innerHTML += `
                    <label class="flex items-center">
                        <input type="checkbox" value="${i}. Ceza Dairesi" class="ceza-checkbox mr-2">
                        <span class="text-gray-700 dark:text-gray-300">${i}. Ceza</span>
                    </label>
                `;
            }
        }

        function selectAllHukuk() {
            document.querySelectorAll('.hukuk-checkbox').forEach(cb => cb.checked = true);
        }

        function deselectAllHukuk() {
            document.querySelectorAll('.hukuk-checkbox').forEach(cb => cb.checked = false);
        }

        function selectAllCeza() {
            document.querySelectorAll('.ceza-checkbox').forEach(cb => cb.checked = true);
        }

        function deselectAllCeza() {
            document.querySelectorAll('.ceza-checkbox').forEach(cb => cb.checked = false);
        }

        function getAdvancedFilters() {
            // Don't use advanced filters for semantic search
            if (searchType === 'semantic') {
                return null;
            }

            const filters = {
                kurullar: [],
                hukukDaireler: [],
                cezaDaireler: [],
                esasYil: document.getElementById('esasYil').value,
                esasIlkSiraNo: document.getElementById('esasIlk').value,
                esasSonSiraNo: document.getElementById('esasSon').value,
                kararYil: document.getElementById('kararYil').value,
                kararIlkSiraNo: document.getElementById('kararIlk').value,
                kararSonSiraNo: document.getElementById('kararSon').value,
                baslangicTarihi: document.getElementById('tarihBaslangic').value,
                bitisTarihi: document.getElementById('tarihBitis').value,
                sortBy: document.getElementById('siralamaKriteri').value,
                sortOrder: document.getElementById('siralamaYonu').value
            };

            // Get selected kurullar
            document.querySelectorAll('.kurul-checkbox:checked').forEach(cb => {
                filters.kurullar.push(cb.value);
            });

            // Get selected hukuk daireler
            document.querySelectorAll('.hukuk-checkbox:checked').forEach(cb => {
                filters.hukukDaireler.push(cb.value);
            });

            // Get selected ceza daireler
            document.querySelectorAll('.ceza-checkbox:checked').forEach(cb => {
                filters.cezaDaireler.push(cb.value);
            });

            return filters;
        }
        
        function applyFilters() {
            loadDecisions(1);
            // Close the filter panel after applying
            const panel = document.getElementById('advancedFilters');
            const btn = document.getElementById('toggleFiltersBtn');
            panel.classList.add('hidden');
            btn.innerHTML = '<i class="fas fa-filter mr-2"></i>Gelişmiş Filtreler';
        }
        
        function clearAllFilters() {
            // Clear all checkboxes
            document.querySelectorAll('.kurul-checkbox, .hukuk-checkbox, .ceza-checkbox').forEach(cb => {
                cb.checked = false;
            });
            
            // Clear all input fields
            document.getElementById('esasYil').value = '';
            document.getElementById('esasIlk').value = '';
            document.getElementById('esasSon').value = '';
            document.getElementById('kararYil').value = '';
            document.getElementById('kararIlk').value = '';
            document.getElementById('kararSon').value = '';
            document.getElementById('tarihBaslangic').value = '';
            document.getElementById('tarihBitis').value = '';
            document.getElementById('siralamaKriteri').value = '1';
            document.getElementById('siralamaYonu').value = 'desc';
            
            // Reset the simple daire filter
            document.getElementById('daireFilter').value = '';
            currentDaire = '';
            
            // Reload without filters
            loadDecisions(1);
        }
    </script>
</body>
</html>