package main

import "time"

type Decision struct {
	ID               string    `json:"id"` // Changed to string for consistency
	DecisionID       string    `json:"decision_id"`
	Daire            string    `json:"daire"`
	EsasNo           string    `json:"esas_no"`
	Karar<PERSON>o          string    `json:"karar_no"`
	Kara<PERSON><PERSON><PERSON><PERSON>      string    `json:"karar_tarihi"`
	MahkemeAdi       string    `json:"mahkeme_adi"`
	IlamT<PERSON>hi       string    `json:"ilam_tarihi"`
	DecisionText     string    `json:"decision_text"`
	RawHTML          string    `json:"raw_html"`
	CreatedAt        time.Time `json:"created_at"`
	WorkerID         string    `json:"worker_id,omitempty"`
	BatchID          int       `json:"batch_id,omitempty"`
	ProcessingTimeMs int64     `json:"processing_time_ms,omitempty"`
}

type SearchRequest struct {
	PageNumber int `json:"page_number"`
	PageSize   int `json:"page_size"`
}

type APISearchRequest struct {
	Data APISearchData `json:"data"`
}

type APISearchData struct {
	ArananKelime        string `json:"arananKelime"`
	YargitayMah         string `json:"yargitayMah"`
	Hukuk               string `json:"hukuk"`
	Ceza                string `json:"ceza"`
	EsasYil             string `json:"esasYil"`
	EsasIlkSiraNo       string `json:"esasIlkSiraNo"`
	EsasSonSiraNo       string `json:"esasSonSiraNo"`
	KararYil            string `json:"kararYil"`
	KararIlkSiraNo      string `json:"kararIlkSiraNo"`
	KararSonSiraNo      string `json:"kararSonSiraNo"`
	BaslangicTarihi     string `json:"baslangicTarihi"`
	BitisTarihi         string `json:"bitisTarihi"`
	Siralama            string `json:"siralama"`
	SiralamaDirection   string `json:"siralamaDirection"`
	BirimYrgKurulDaire  string `json:"birimYrgKurulDaire"`
	BirimYrgHukukDaire  string `json:"birimYrgHukukDaire"`
	BirimYrgCezaDaire   string `json:"birimYrgCezaDaire"`
	PageSize            int    `json:"pageSize"`
	PageNumber          int    `json:"pageNumber"`
}

type APISearchResponse struct {
	Data struct {
		Data            []APISearchItem `json:"data"`
		RecordsTotal    int            `json:"recordsTotal"`
		RecordsFiltered int            `json:"recordsFiltered"`
	} `json:"data"`
}

type APISearchItem struct {
	ID          string `json:"id"`
	Daire       string `json:"daire"`
	EsasNo      string `json:"esasNo"`
	KararNo     string `json:"kararNo"`
	KararTarihi string `json:"kararTarihi"`
	Index       int    `json:"index"`
	SiraNo      int    `json:"siraNo"`
}

type APIDocumentResponse struct {
	Data string `json:"data"`
}

// SimilarDecision represents a decision with similarity score for semantic search
type SimilarDecision struct {
	DecisionID   string  `json:"decision_id"`
	Daire        string  `json:"daire"`
	EsasNo       string  `json:"esas_no"`
	KararNo      string  `json:"karar_no"`
	KararTarihi  string  `json:"karar_tarihi"`
	DecisionText string  `json:"decision_text"`
	Similarity   float32 `json:"similarity"`
}