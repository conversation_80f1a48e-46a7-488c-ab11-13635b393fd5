package main

import (
	"log"
	"strconv"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func StartGinServer(db *Database, port string) {
	gin.SetMode(gin.ReleaseMode)
	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(cors.Default())

	// Static files
	r.Static("/static", "./static")
	r.StaticFile("/", "./static/index.html")

	// Embedding manager
	embeddingManager := NewEmbeddingManager(db)

	// API routes
	api := r.Group("/api")
	{
		api.GET("/stats", func(c *gin.Context) {
			stats, err := db.GetStats()
			if err != nil {
				c.JSON(500, gin.H{"error": err.Error()})
				return
			}
			
			// Add embedding statistics
			embeddingStats, err := db.GetEmbeddingStats()
			if err == nil {
				stats["embeddings"] = embeddingStats
			}
			
			c.<PERSON>(200, stats)
		})

		api.GET("/decisions", func(c *gin.Context) {
			page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
			limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
			search := c.Query("search")
			daire := c.Query("daire")
			
			decisions, total, err := db.GetDecisions(page, limit, search, daire)
			if err != nil {
				c.JSON(500, gin.H{"error": err.Error()})
				return
			}
			
			c.JSON(200, gin.H{
				"decisions":   decisions,
				"page":        page,
				"limit":       limit,
				"total":       total,
				"total_pages": (total + limit - 1) / limit,
			})
		})

		api.GET("/decisions/:id", func(c *gin.Context) {
			id := c.Param("id")
			decision, err := db.GetDecisionByID(id)
			if err != nil {
				c.JSON(404, gin.H{"error": "Decision not found"})
				return
			}
			c.JSON(200, decision)
		})

		api.GET("/daires", func(c *gin.Context) {
			daires, err := db.GetDaires()
			if err != nil {
				c.JSON(500, gin.H{"error": err.Error()})
				return
			}
			c.JSON(200, daires)
		})

		api.GET("/search", func(c *gin.Context) {
			query := c.Query("q")
			if query == "" {
				c.JSON(400, gin.H{"error": "Query required"})
				return
			}
			
			page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
			results, total, err := db.SearchDecisions(query, page, 10)
			if err != nil {
				c.JSON(500, gin.H{"error": err.Error()})
				return
			}
			
			c.JSON(200, gin.H{
				"results": results,
				"query":   query,
				"page":    page,
				"total":   total,
			})
		})

		api.GET("/semantic-search", func(c *gin.Context) {
			query := c.Query("q")
			if query == "" {
				c.JSON(400, gin.H{"error": "Query required"})
				return
			}
			
			limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
			results, err := embeddingManager.SemanticSearch(query, limit)
			if err != nil {
				c.JSON(500, gin.H{"error": err.Error()})
				return
			}
			
			c.JSON(200, gin.H{
				"results": results,
				"query":   query,
				"count":   len(results),
				"type":    "semantic_search",
				"model":   "BGE-M3",
			})
		})

		api.GET("/embedding/health", func(c *gin.Context) {
			err := embeddingManager.CheckServiceHealth()
			if err != nil {
				c.JSON(503, gin.H{"status": "unhealthy", "error": err.Error()})
				return
			}
			c.JSON(200, gin.H{"status": "healthy", "model": "BGE-M3"})
		})

		api.POST("/embedding/process-batch", func(c *gin.Context) {
			limit, _ := strconv.Atoi(c.DefaultQuery("limit", "100"))
			err := embeddingManager.ProcessBatchEmbeddings(limit)
			if err != nil {
				c.JSON(500, gin.H{"error": err.Error()})
				return
			}
			c.JSON(200, gin.H{"status": "completed", "processed": limit})
		})
	}

	// Health check
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "healthy"})
	})

	log.Printf("🚀 Gin server starting on %s", port)
	r.Run(port)
}