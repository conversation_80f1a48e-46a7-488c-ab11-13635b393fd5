.PHONY: help run dev build clean test install-air db-stats db-clean

help: ## Yardım
	@echo "Yargı<PERSON>y Scraper v3 - Komutlar:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install-air: ## Air'i yükle (hot reload)
	go install github.com/cosmtrek/air@latest

dev: ## Air ile geliştirme modu
	air

run: ## Uygulamayı çalıştır
	go run . -start=1 -end=5

build: ## Binary oluştur
	go build -o yargitay-scraper .

clean: ## Temizlik
	rm -rf tmp/
	rm -f yargitay-scraper
	rm -f *.log

test: ## Testleri çalıştır
	go test -v ./...

fmt: ## Kodu formatla
	go fmt ./...

vet: ## Go vet çalıştır
	go vet ./...

deps: ## Bağımlılıkları indir
	go mod download
	go mod tidy

db-stats: ## Veritabanı istatistikleri
	@echo "📊 Veritabanı İstatistikleri:"
	@sqlite3 data/decisions.db "SELECT COUNT(*) as 'Toplam Karar' FROM decisions;" 2>/dev/null || echo "Henüz veri yok"
	@sqlite3 data/decisions.db "SELECT daire, COUNT(*) as count FROM decisions WHERE daire IS NOT NULL GROUP BY daire ORDER BY count DESC LIMIT 5;" 2>/dev/null || true

db-clean: ## Veritabanını sil (DİKKAT!)
	rm -f data/decisions.db data/decisions.db-shm data/decisions.db-wal
	@echo "⚠️ Veritabanı silindi!"

quick-test: ## Hızlı test (1 sayfa)
	go run . -start=1 -end=1 -size=5 -no-server

full-scan: ## Tam tarama (1-100 sayfa)
	go run . -start=1 -end=100 -size=50