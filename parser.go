package main

import (
	"regexp"
	"strings"
)

type Parser struct {
	textCleanRegex  *regexp.Regexp
}

func NewParser() *Parser {
	return &Parser{
		textCleanRegex: regexp.MustCompile(`<[^>]+>`),
	}
}

func (p *Parser) ParseHTML(html string, decisionID string) *Decision {
	decision := &Decision{
		DecisionID: decisionID,
		RawHTML:    html, // Keep original HTML for display
	}

	// Create clean text for search purposes (remove HTML tags)
	cleanText := p.textCleanRegex.ReplaceAllString(html, " ")
	cleanText = strings.ReplaceAll(cleanText, "&nbsp;", " ")
	cleanText = strings.ReplaceAll(cleanText, "&lt;", "<")
	cleanText = strings.ReplaceAll(cleanText, "&gt;", ">")
	cleanText = strings.ReplaceAll(cleanText, "&amp;", "&")
	cleanText = strings.ReplaceAll(cleanText, "&quot;", "\"")
	cleanText = strings.ReplaceAll(cleanText, "<br>", "\n")
	cleanText = strings.ReplaceAll(cleanText, "<br/>", "\n")
	cleanText = strings.ReplaceAll(cleanText, "<br />", "\n")
	cleanText = strings.ReplaceAll(cleanText, "\r", "")
	cleanText = regexp.MustCompile(`\s+`).ReplaceAllString(cleanText, " ")
	cleanText = strings.TrimSpace(cleanText)

	decision.DecisionText = cleanText

	// No need to parse anything else - API provides all metadata
	
	return decision
}