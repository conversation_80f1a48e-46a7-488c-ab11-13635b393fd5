package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"
)

type EmbeddingService struct {
	baseURL    string
	httpClient *http.Client
}

type EmbeddingRequest struct {
	Texts []string `json:"texts"`
}

type EmbeddingResponse struct {
	Embeddings     [][]float32 `json:"embeddings"`
	Count          int         `json:"count"`
	Dimensions     int         `json:"dimensions"`
	ProcessingTime float64     `json:"processing_time"`
	Model          string      `json:"model"`
	Error          string      `json:"error,omitempty"`
}

type BatchEmbeddingRequest struct {
	Decisions []BatchDecision `json:"decisions"`
}

type BatchDecision struct {
	DecisionID   string `json:"decision_id"`
	DecisionText string `json:"decision_text"`
}

type BatchEmbeddingResponse struct {
	Results        []EmbeddingResult `json:"results"`
	TotalProcessed int               `json:"total_processed"`
	Model          string            `json:"model"`
	Error          string            `json:"error,omitempty"`
}

type EmbeddingResult struct {
	DecisionID string    `json:"decision_id"`
	Embedding  []float32 `json:"embedding"`
}

type SimilarityRequest struct {
	Query string   `json:"query"`
	Texts []string `json:"texts"`
}

type SimilarityResponse struct {
	Similarities []float32 `json:"similarities"`
	Query        string    `json:"query"`
	Count        int       `json:"count"`
	Error        string    `json:"error,omitempty"`
}

// NewEmbeddingService creates a new embedding service client
func NewEmbeddingService() *EmbeddingService {
	baseURL := os.Getenv("EMBEDDING_SERVICE_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8021" // Default for local development
	}

	return &EmbeddingService{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 60 * time.Second, // Long timeout for model processing
		},
	}
}

// HealthCheck checks if the embedding service is healthy
func (e *EmbeddingService) HealthCheck() error {
	resp, err := e.httpClient.Get(e.baseURL + "/health")
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("embedding service unhealthy: status %d", resp.StatusCode)
	}

	return nil
}

// GenerateEmbeddings generates embeddings for given texts
func (e *EmbeddingService) GenerateEmbeddings(texts []string) ([][]float32, error) {
	if len(texts) == 0 {
		return nil, fmt.Errorf("no texts provided")
	}

	reqData := EmbeddingRequest{Texts: texts}
	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	resp, err := e.httpClient.Post(
		e.baseURL+"/embed",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return nil, fmt.Errorf("embedding request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var embResp EmbeddingResponse
	if err := json.Unmarshal(body, &embResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if embResp.Error != "" {
		return nil, fmt.Errorf("embedding service error: %s", embResp.Error)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("embedding service returned status %d: %s", resp.StatusCode, embResp.Error)
	}

	log.Printf("✅ Generated %d embeddings (%dd) in %.3fs",
		embResp.Count, embResp.Dimensions, embResp.ProcessingTime)

	return embResp.Embeddings, nil
}

// ProcessBatch processes multiple decisions in batch
func (e *EmbeddingService) ProcessBatch(decisions []Decision) ([]EmbeddingResult, error) {
	if len(decisions) == 0 {
		return nil, fmt.Errorf("no decisions provided")
	}

	// Convert to batch format
	batchDecisions := make([]BatchDecision, len(decisions))
	for i, decision := range decisions {
		batchDecisions[i] = BatchDecision{
			DecisionID:   decision.DecisionID,
			DecisionText: decision.DecisionText,
		}
	}

	reqData := BatchEmbeddingRequest{Decisions: batchDecisions}
	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal batch request: %w", err)
	}

	resp, err := e.httpClient.Post(
		e.baseURL+"/embed-batch",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return nil, fmt.Errorf("batch embedding request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read batch response: %w", err)
	}

	var batchResp BatchEmbeddingResponse
	if err := json.Unmarshal(body, &batchResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal batch response: %w", err)
	}

	if batchResp.Error != "" {
		return nil, fmt.Errorf("batch embedding service error: %s", batchResp.Error)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("batch embedding service returned status %d", resp.StatusCode)
	}

	log.Printf("✅ Processed %d decisions in batch", batchResp.TotalProcessed)

	return batchResp.Results, nil
}

// ComputeSimilarity computes similarity between query and texts
func (e *EmbeddingService) ComputeSimilarity(query string, texts []string) ([]float32, error) {
	reqData := SimilarityRequest{
		Query: query,
		Texts: texts,
	}

	jsonData, err := json.Marshal(reqData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal similarity request: %w", err)
	}

	resp, err := e.httpClient.Post(
		e.baseURL+"/similarity",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return nil, fmt.Errorf("similarity request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read similarity response: %w", err)
	}

	var simResp SimilarityResponse
	if err := json.Unmarshal(body, &simResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal similarity response: %w", err)
	}

	if simResp.Error != "" {
		return nil, fmt.Errorf("similarity service error: %s", simResp.Error)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("similarity service returned status %d", resp.StatusCode)
	}

	return simResp.Similarities, nil
}

// EmbeddingManager manages embedding operations for the application
type EmbeddingManager struct {
	service *EmbeddingService
	db      *Database
	queue   chan Decision
	workers int
}

// NewEmbeddingManager creates a new embedding manager
func NewEmbeddingManager(db *Database) *EmbeddingManager {
	em := &EmbeddingManager{
		service: NewEmbeddingService(),
		db:      db,
		queue:   make(chan Decision, 100), // Buffer for 100 decisions
		workers: 3,                        // 3 concurrent workers
	}
	em.StartWorkers()
	return em
}

// StartWorkers starts the embedding worker pool
func (em *EmbeddingManager) StartWorkers() {
	for i := 0; i < em.workers; i++ {
		go em.worker(i + 1)
	}
	log.Printf("🚀 Embedding worker pool started with %d workers", em.workers)
}

// worker processes decisions from the queue
func (em *EmbeddingManager) worker(id int) {
	for decision := range em.queue {
		if err := em.ProcessSingleDecision(decision); err != nil {
			log.Printf("⚠️ [EW%d] ID %s için embedding oluşturulamadı: %v", id, decision.DecisionID, err)
		} else {
			log.Printf("🧠 [EW%d] ID %s için embedding oluşturuldu", id, decision.DecisionID)
		}
		// No delay - let the embedding service handle rate limiting
	}
}

// QueueDecision adds a decision to the embedding queue
func (em *EmbeddingManager) QueueDecision(decision Decision) {
	select {
	case em.queue <- decision:
		// Successfully queued
	default:
		// Queue is full, log and skip
		log.Printf("⚠️ Embedding queue full, skipping ID %s", decision.DecisionID)
	}
}

// ProcessNewDecision processes a newly scraped decision for embeddings
func (em *EmbeddingManager) ProcessNewDecision(decision *Decision) error {
	if decision.DecisionText == "" {
		log.Printf("⚠️ Skipping embedding for empty decision: %s", decision.DecisionID)
		return nil
	}

	// Generate embedding
	embeddings, err := em.service.GenerateEmbeddings([]string{decision.DecisionText})
	if err != nil {
		return fmt.Errorf("failed to generate embedding for %s: %w", decision.DecisionID, err)
	}

	if len(embeddings) == 0 {
		return fmt.Errorf("no embeddings generated for %s", decision.DecisionID)
	}

	// Save embedding to database
	if err := em.db.SaveEmbedding(decision.DecisionID, embeddings[0], "BGE-M3"); err != nil {
		return fmt.Errorf("failed to save embedding for %s: %w", decision.DecisionID, err)
	}

	log.Printf("✅ Generated and saved embedding for decision %s (%d dimensions)",
		decision.DecisionID, len(embeddings[0]))

	return nil
}

// ProcessBatchEmbeddings processes multiple decisions without embeddings
func (em *EmbeddingManager) ProcessBatchEmbeddings(limit int) error {
	// Get decisions without embeddings
	decisions, err := em.db.GetDecisionsWithoutEmbeddings(limit)
	if err != nil {
		return fmt.Errorf("failed to get decisions without embeddings: %w", err)
	}

	if len(decisions) == 0 {
		log.Printf("📊 No decisions without embeddings found")
		return nil
	}

	log.Printf("🔄 Processing %d decisions for embeddings...", len(decisions))

	// Process in batches using the embedding service
	results, err := em.service.ProcessBatch(decisions)
	if err != nil {
		return fmt.Errorf("failed to process batch embeddings: %w", err)
	}

	// Save results to database
	successCount := 0
	for _, result := range results {
		if err := em.db.SaveEmbedding(result.DecisionID, result.Embedding, "BGE-M3"); err != nil {
			log.Printf("❌ Failed to save embedding for %s: %v", result.DecisionID, err)
		} else {
			successCount++
		}
	}

	log.Printf("✅ Successfully processed %d/%d decisions for embeddings", successCount, len(decisions))
	return nil
}

// SemanticSearch performs semantic similarity search
func (em *EmbeddingManager) SemanticSearch(query string, limit int) ([]SimilarDecision, error) {
	if query == "" {
		return nil, fmt.Errorf("empty query")
	}

	// Generate embedding for the query
	queryEmbeddings, err := em.service.GenerateEmbeddings([]string{query})
	if err != nil {
		return nil, fmt.Errorf("failed to generate query embedding: %w", err)
	}

	if len(queryEmbeddings) == 0 {
		return nil, fmt.Errorf("no embedding generated for query")
	}

	// Search for similar decisions in database
	results, err := em.db.SearchSimilar(queryEmbeddings[0], limit)
	if err != nil {
		return nil, fmt.Errorf("failed to search similar decisions: %w", err)
	}

	queryPreview := query
	if len(query) > 50 {
		queryPreview = query[:50] + "..."
	}
	log.Printf("🔍 Found %d similar decisions for query: %s", len(results), queryPreview)
	return results, nil
}

// CheckServiceHealth verifies the embedding service is running
func (em *EmbeddingManager) CheckServiceHealth() error {
	return em.service.HealthCheck()
}

// ProcessSingleDecision generates and saves embedding for a single decision
func (em *EmbeddingManager) ProcessSingleDecision(decision Decision) error {
	if decision.DecisionText == "" {
		return fmt.Errorf("decision has no text")
	}

	// Generate embedding
	embeddings, err := em.service.GenerateEmbeddings([]string{decision.DecisionText})
	if err != nil {
		return fmt.Errorf("failed to generate embedding: %w", err)
	}

	if len(embeddings) == 0 {
		return fmt.Errorf("no embedding generated")
	}

	// Save to database
	if err := em.db.SaveEmbedding(decision.DecisionID, embeddings[0], "BGE-M3"); err != nil {
		return fmt.Errorf("failed to save embedding: %w", err)
	}

	return nil
}
