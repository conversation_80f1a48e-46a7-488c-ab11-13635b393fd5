# Development Dockerfile with Air for hot reload
FROM golang:1.24-alpine

# Install required packages
RUN apk add --no-cache git gcc musl-dev

# Install Air for hot reload
RUN go install github.com/air-verse/air@latest

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy air config if exists, otherwise Air will use defaults
COPY .air.toml* ./

# Expose ports
EXPOSE 8020

# Volume for persistent data
VOLUME ["/app/data"]

# Use Air for hot reload with mod=readonly to ignore vendor
CMD ["air", "-c", ".air.toml"]