# Binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
main
yargitay-scraper

# Test binary
*.test

# Output
*.out

# Go workspace
go.work

# Air tmp directory
tmp/

# Database
data/*.db
data/*.db-shm
data/*.db-wal

# Environment
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log

# Vendor (if needed)
vendor/

# Compiled binary
hukukat

# Claude Code
.claude/

# Python cache
__pycache__/
*.pyc

# Test/Debug files
debug_*.py
test_*.py